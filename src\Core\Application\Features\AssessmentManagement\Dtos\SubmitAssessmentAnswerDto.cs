namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for submitting individual answers
    /// Arabic: كائل نقل البيانات لإرسال الإجابات الفردية
    /// </summary>
    public record SubmitAssessmentAnswerDto
    {
        /// <summary>
        /// Question ID this answer belongs to
        /// Arabic: معرف السؤال
        /// </summary>
        public int QuestionId { get; set; }

        /// <summary>
        /// Text answer (for text-type questions)
        /// Arabic: الإجابة النصية
        /// </summary>
        public string? TextAnswer { get; set; }

        /// <summary>
        /// Selected option IDs (for choice-type questions)
        /// Arabic: معرفات الخيارات المختارة
        /// </summary>
        public List<int>? SelectedOptionIds { get; set; }
    }
}
