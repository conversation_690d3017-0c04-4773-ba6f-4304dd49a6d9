using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for AssessmentQuestion entity
    /// Configures navigation properties, relationships, and constraints for assessment questions
    /// Provides comprehensive configuration for question management within assessments
    /// </summary>
    public class AssessmentQuestionEntityConfig : IEntityTypeConfiguration<AssessmentQuestion>
    {
        public void Configure(EntityTypeBuilder<AssessmentQuestion> builder)
        {
            // Configure primary key
            builder.HasKey(aq => aq.Id);

            // Configure required properties with constraints
            builder.Property(aq => aq.QuestionText)
                .IsRequired()
                .HasMaxLength(1000)
                .HasComment("Text content of the question");

            builder.Property(aq => aq.QuestionType)
                .IsRequired()
                .HasComment("Type of question (SingleChoice, MultiChoice, or Text)");

            builder.Property(aq => aq.AssessmentId)
                .IsRequired()
                .HasComment("Foreign key reference to the Assessment this question belongs to");

            builder.Property(aq => aq.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0)
                .HasComment("Display order of the question within the assessment");

            builder.Property(aq => aq.IsRequired)
                .IsRequired()
                .HasDefaultValue(true)
                .HasComment("Indicates if this question is required to be answered");

            // Configure relationships
            builder.HasOne(aq => aq.Assessment)
                .WithMany(a => a.Questions)
                .HasForeignKey(aq => aq.AssessmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentQuestions_Assessments_AssessmentId");

            // Configure one-to-many relationships
            builder.HasMany(aq => aq.Answers)
                .WithOne(a => a.Question)
                .HasForeignKey(a => a.QuestionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Answers_AssessmentQuestions_QuestionId");

            builder.HasMany(aq => aq.Options)
                .WithOne(o => o.Question)
                .HasForeignKey(o => o.QuestionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Options_AssessmentQuestions_QuestionId");

            // Configure indexes for performance optimization
            builder.HasIndex(aq => aq.AssessmentId)
                .HasDatabaseName("IX_AssessmentQuestions_AssessmentId_Performance");

            builder.HasIndex(aq => aq.QuestionType)
                .HasDatabaseName("IX_AssessmentQuestions_QuestionType_Performance");

            builder.HasIndex(aq => new { aq.AssessmentId, aq.DisplayOrder })
                .HasDatabaseName("IX_AssessmentQuestions_AssessmentId_DisplayOrder_Performance");

            builder.HasIndex(aq => aq.IsRequired)
                .HasDatabaseName("IX_AssessmentQuestions_IsRequired_Performance");

            // Configure table name
            builder.ToTable("AssessmentQuestions");

            // Configure audit properties (inherited from FullAuditedEntity)
            builder.Property(aq => aq.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the question was created");

            builder.Property(aq => aq.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the question was last updated");

            builder.Property(aq => aq.IsDeleted)
                .HasDefaultValue(false)
                .HasComment("Soft delete flag");

           
        }
    }
}
