﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class _AssessmentTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Assessments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FundId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the Fund this assessment belongs to"),
                    Title = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "Assessment title for display purposes"),
                    Type = table.Column<int>(type: "int", nullable: false, comment: "Assessment type (Questionnaire or Attachment)"),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 1, comment: "Current status of the assessment"),
                    AttachmentId = table.Column<int>(type: "int", nullable: true, comment: "Foreign key reference to the Attachment entity (required for Attachment type assessments)"),
                    ReviewerComments = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "Comments from reviewer when assessment is rejected"),
                    ReviewedBy = table.Column<int>(type: "int", nullable: true, comment: "User ID of the reviewer who approved/rejected the assessment"),
                    ReviewedDate = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "Date when the assessment was reviewed (approved/rejected)"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the assessment was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the assessment was last updated"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedByUserId = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false, comment: "Soft delete flag"),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedByUserId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Assessments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Assessments_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Assessments_AspNetUsers_DeletedByUserId",
                        column: x => x.DeletedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_Attachments_AttachmentId",
                        column: x => x.AttachmentId,
                        principalTable: "Attachments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_Funds_FundId",
                        column: x => x.FundId,
                        principalTable: "Funds",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_Users_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_Users_ReviewedBy",
                        column: x => x.ReviewedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Assessments_Users_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AssessmentQuestions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AssessmentId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the Assessment this question belongs to"),
                    QuestionText = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false, comment: "Text content of the question"),
                    QuestionType = table.Column<int>(type: "int", nullable: false, comment: "Type of question (SingleChoice, MultiChoice, or Text)"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0, comment: "Display order of the question within the assessment"),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false, defaultValue: true, comment: "Indicates if this question is required to be answered"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the question was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the question was last updated"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedByUserId = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false, comment: "Soft delete flag"),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedByUserId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AssessmentQuestions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_AspNetUsers_DeletedByUserId",
                        column: x => x.DeletedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_Assessments_AssessmentId",
                        column: x => x.AssessmentId,
                        principalTable: "Assessments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_Users_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentQuestions_Users_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AssessmentResponses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AssessmentId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the Assessment this response is for"),
                    UserId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the User (Board Member) who submitted this response"),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 1, comment: "Status of the response (Pending or Completed)"),
                    SubmissionDate = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "Date when the response was submitted"),
                    GeneralComments = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true, comment: "General comments provided by the board member"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the response was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the response was last updated"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedByUserId = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false, comment: "Soft delete flag"),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedByUserId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AssessmentResponses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_AspNetUsers_DeletedByUserId",
                        column: x => x.DeletedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_Assessments_AssessmentId",
                        column: x => x.AssessmentId,
                        principalTable: "Assessments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_Users_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_Users_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentResponses_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AssessmentStatusHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AssessmentId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the Assessment entity"),
                    AssessmentStatusId = table.Column<int>(type: "int", nullable: false, comment: "Assessment status ID at the time of this history entry"),
                    Action = table.Column<int>(type: "int", nullable: false, comment: "Action that was performed (enum value)"),
                    Reason = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "Reason for the action/transition"),
                    RejectionReason = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "Rejection reason (specific for rejection actions)"),
                    ActionDetails = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true, comment: "Comprehensive description of the operation performed"),
                    Notes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "Localization key reference (NOT translated text) for retrieval-time localization"),
                    UserRole = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "User role performing the action"),
                    PreviousStatus = table.Column<int>(type: "int", nullable: true, comment: "Previous status (for status changes)"),
                    NewStatus = table.Column<int>(type: "int", nullable: true, comment: "New status (for status changes)"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the status history entry was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false, comment: "User ID who created this status history entry")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AssessmentStatusHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AssessmentStatusHistories_Assessments_AssessmentId",
                        column: x => x.AssessmentId,
                        principalTable: "Assessments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AssessmentStatusHistories_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Options",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    QuestionId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the AssessmentQuestion this option belongs to"),
                    Value = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true, comment: "The option value/text displayed to users"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the option was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the option was last updated"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedByUserId = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false, comment: "Soft delete flag"),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedByUserId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Options", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Options_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Options_AspNetUsers_DeletedByUserId",
                        column: x => x.DeletedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Options_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Options_AssessmentQuestions_QuestionId",
                        column: x => x.QuestionId,
                        principalTable: "AssessmentQuestions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Options_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Options_Users_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Options_Users_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Answers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ResponseId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the AssessmentResponse this answer belongs to"),
                    QuestionId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the AssessmentQuestion this answer is for"),
                    AnswerValue = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true, comment: "The actual answer value provided by the board member (for text questions)"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the answer was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the answer was last updated"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedByUserId = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false, comment: "Soft delete flag"),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedByUserId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Answers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Answers_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Answers_AspNetUsers_DeletedByUserId",
                        column: x => x.DeletedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Answers_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Answers_AssessmentQuestions_QuestionId",
                        column: x => x.QuestionId,
                        principalTable: "AssessmentQuestions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Answers_AssessmentResponses_ResponseId",
                        column: x => x.ResponseId,
                        principalTable: "AssessmentResponses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Answers_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Answers_Users_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Answers_Users_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AnswerOptions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    OptionId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the Option that was selected"),
                    AnswerId = table.Column<int>(type: "int", nullable: false, comment: "Foreign key reference to the Answer this option selection belongs to"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the answer option was created"),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()", comment: "Timestamp when the answer option was last updated"),
                    UpdatedBy = table.Column<int>(type: "int", nullable: true),
                    UpdatedByUserId = table.Column<int>(type: "int", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: true, defaultValue: false, comment: "Soft delete flag"),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    DeletedByUserId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AnswerOptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AnswerOptions_Answers_AnswerId",
                        column: x => x.AnswerId,
                        principalTable: "Answers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AnswerOptions_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AnswerOptions_AspNetUsers_DeletedByUserId",
                        column: x => x.DeletedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AnswerOptions_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AnswerOptions_Options_OptionId",
                        column: x => x.OptionId,
                        principalTable: "Options",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AnswerOptions_Users_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AnswerOptions_Users_DeletedBy",
                        column: x => x.DeletedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AnswerOptions_Users_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_AnswerId_OptionId_Unique",
                table: "AnswerOptions",
                columns: new[] { "AnswerId", "OptionId" },
                unique: true,
                filter: "[IsDeleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_AnswerId_Performance",
                table: "AnswerOptions",
                column: "AnswerId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_CreatedBy",
                table: "AnswerOptions",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_CreatedByUserId",
                table: "AnswerOptions",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_DeletedBy",
                table: "AnswerOptions",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_DeletedByUserId",
                table: "AnswerOptions",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_OptionId_Performance",
                table: "AnswerOptions",
                column: "OptionId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_UpdatedBy",
                table: "AnswerOptions",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_UpdatedByUserId",
                table: "AnswerOptions",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_CreatedBy",
                table: "Answers",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_CreatedByUserId",
                table: "Answers",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_DeletedBy",
                table: "Answers",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_DeletedByUserId",
                table: "Answers",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_QuestionId_Performance",
                table: "Answers",
                column: "QuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_ResponseId_Performance",
                table: "Answers",
                column: "ResponseId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_ResponseId_QuestionId_Unique",
                table: "Answers",
                columns: new[] { "ResponseId", "QuestionId" },
                unique: true,
                filter: "[IsDeleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_UpdatedBy",
                table: "Answers",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_UpdatedByUserId",
                table: "Answers",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_AssessmentId_DisplayOrder_Performance",
                table: "AssessmentQuestions",
                columns: new[] { "AssessmentId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_AssessmentId_Performance",
                table: "AssessmentQuestions",
                column: "AssessmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_CreatedBy",
                table: "AssessmentQuestions",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_CreatedByUserId",
                table: "AssessmentQuestions",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_DeletedBy",
                table: "AssessmentQuestions",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_DeletedByUserId",
                table: "AssessmentQuestions",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_IsRequired_Performance",
                table: "AssessmentQuestions",
                column: "IsRequired");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_QuestionType_Performance",
                table: "AssessmentQuestions",
                column: "QuestionType");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_UpdatedBy",
                table: "AssessmentQuestions",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_UpdatedByUserId",
                table: "AssessmentQuestions",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_AssessmentId_Performance",
                table: "AssessmentResponses",
                column: "AssessmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_AssessmentId_UserId_Unique",
                table: "AssessmentResponses",
                columns: new[] { "AssessmentId", "UserId" },
                unique: true,
                filter: "[IsDeleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_CreatedBy",
                table: "AssessmentResponses",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_CreatedByUserId",
                table: "AssessmentResponses",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_DeletedBy",
                table: "AssessmentResponses",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_DeletedByUserId",
                table: "AssessmentResponses",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_Status_Performance",
                table: "AssessmentResponses",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_SubmissionDate_Performance",
                table: "AssessmentResponses",
                column: "SubmissionDate",
                filter: "[SubmissionDate] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_UpdatedBy",
                table: "AssessmentResponses",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_UpdatedByUserId",
                table: "AssessmentResponses",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_UserId_Performance",
                table: "AssessmentResponses",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_AttachmentId",
                table: "Assessments",
                column: "AttachmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_CreatedAt_Performance",
                table: "Assessments",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_CreatedBy",
                table: "Assessments",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_CreatedByUserId",
                table: "Assessments",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_DeletedBy",
                table: "Assessments",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_DeletedByUserId",
                table: "Assessments",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_FundId_Performance",
                table: "Assessments",
                column: "FundId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_ReviewedBy_Performance",
                table: "Assessments",
                column: "ReviewedBy",
                filter: "[ReviewedBy] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_ReviewedDate_Performance",
                table: "Assessments",
                column: "ReviewedDate",
                filter: "[ReviewedDate] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_Status_Performance",
                table: "Assessments",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_Type_Performance",
                table: "Assessments",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_UpdatedBy",
                table: "Assessments",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_UpdatedByUserId",
                table: "Assessments",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_Action_Performance",
                table: "AssessmentStatusHistories",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_AssessmentId_CreatedAt_Performance",
                table: "AssessmentStatusHistories",
                columns: new[] { "AssessmentId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_AssessmentId_Performance",
                table: "AssessmentStatusHistories",
                column: "AssessmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_CreatedAt_Performance",
                table: "AssessmentStatusHistories",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_CreatedBy",
                table: "AssessmentStatusHistories",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_NewStatus_Performance",
                table: "AssessmentStatusHistories",
                column: "NewStatus",
                filter: "[NewStatus] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentStatusHistories_PreviousStatus_Performance",
                table: "AssessmentStatusHistories",
                column: "PreviousStatus",
                filter: "[PreviousStatus] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Options_CreatedBy",
                table: "Options",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Options_CreatedByUserId",
                table: "Options",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Options_DeletedBy",
                table: "Options",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Options_DeletedByUserId",
                table: "Options",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Options_QuestionId_Performance",
                table: "Options",
                column: "QuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_Options_UpdatedBy",
                table: "Options",
                column: "UpdatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Options_UpdatedByUserId",
                table: "Options",
                column: "UpdatedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AnswerOptions");

            migrationBuilder.DropTable(
                name: "AssessmentStatusHistories");

            migrationBuilder.DropTable(
                name: "Answers");

            migrationBuilder.DropTable(
                name: "Options");

            migrationBuilder.DropTable(
                name: "AssessmentResponses");

            migrationBuilder.DropTable(
                name: "AssessmentQuestions");

            migrationBuilder.DropTable(
                name: "Assessments");
        }
    }
}
