using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for Assessment entity
    /// Configures navigation properties, relationships, and constraints for assessment management
    /// Provides comprehensive configuration for assessment workflow and audit trail
    /// </summary>
    public class AssessmentEntityConfig : IEntityTypeConfiguration<Assessment>
    {
        public void Configure(EntityTypeBuilder<Assessment> builder)
        {
            // Configure primary key
            builder.HasKey(a => a.Id);

            // Configure required properties with constraints
            builder.Property(a => a.Title)
                .IsRequired()
                .HasMaxLength(255)
                .HasComment("Assessment title for display purposes");

            builder.Property(a => a.Type)
                .IsRequired()
                .HasComment("Assessment type (Questionnaire or Attachment)");

            builder.Property(a => a.Status)
                .IsRequired()
                .HasDefaultValue(Domain.Entities.AssessmentManagement.Enums.AssessmentStatus.Draft)
                .HasComment("Current status of the assessment");

            builder.Property(a => a.FundId)
                .IsRequired()
                .HasComment("Foreign key reference to the Fund this assessment belongs to");

            // Configure optional properties
            builder.Property(a => a.ReviewerComments)
                .HasMaxLength(2000)
                .HasComment("Comments from reviewer when assessment is rejected");

            builder.Property(a => a.ReviewedDate)
                .HasComment("Date when the assessment was reviewed (approved/rejected)");

            builder.Property(a => a.AttachmentId)
                .HasComment("Foreign key reference to the Attachment entity (required for Attachment type assessments)");

            builder.Property(a => a.ReviewedBy)
                .HasComment("User ID of the reviewer who approved/rejected the assessment");

          //  Configure relationships
            builder.HasOne(a => a.Fund)
                .WithMany(a => a.Assessments)
                .HasForeignKey(a => a.FundId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Assessments_Funds_FundId");

            builder.HasOne(a => a.Reviewer)
                .WithMany()
                .HasForeignKey(a => a.ReviewedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Assessments_Users_ReviewedBy");

            builder.HasOne(a => a.Attachment)
                .WithMany()
                .HasForeignKey(a => a.AttachmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Assessments_Attachments_AttachmentId");

            // Configure one-to-many relationships
            builder.HasMany(a => a.Questions)
                .WithOne(q => q.Assessment)
                .HasForeignKey(q => q.AssessmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentQuestions_Assessments_AssessmentId");

            builder.HasMany(a => a.Responses)
                .WithOne(r => r.Assessment)
                .HasForeignKey(r => r.AssessmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentResponses_Assessments_AssessmentId");

            builder.HasMany(a => a.StatusHistories)
                .WithOne(sh => sh.Assessment)
                .HasForeignKey(sh => sh.AssessmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentStatusHistories_Assessments_AssessmentId");

            // Configure indexes for performance optimization
            builder.HasIndex(a => a.FundId)
                .HasDatabaseName("IX_Assessments_FundId_Performance");

            builder.HasIndex(a => a.Status)
                .HasDatabaseName("IX_Assessments_Status_Performance");

            builder.HasIndex(a => a.Type)
                .HasDatabaseName("IX_Assessments_Type_Performance");

            builder.HasIndex(a => a.CreatedAt)
                .HasDatabaseName("IX_Assessments_CreatedAt_Performance");

            builder.HasIndex(a => a.ReviewedBy)
                .HasDatabaseName("IX_Assessments_ReviewedBy_Performance")
                .HasFilter("[ReviewedBy] IS NOT NULL");

            builder.HasIndex(a => a.ReviewedDate)
                .HasDatabaseName("IX_Assessments_ReviewedDate_Performance")
                .HasFilter("[ReviewedDate] IS NOT NULL");

            // Configure table name
            builder.ToTable("Assessments");

            // Configure audit properties (inherited from FullAuditedEntity)
            builder.Property(a => a.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the assessment was created");

            builder.Property(a => a.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the assessment was last updated");

            builder.Property(a => a.IsDeleted)
                .HasDefaultValue(false)
                .HasComment("Soft delete flag");

        }
    }
}
