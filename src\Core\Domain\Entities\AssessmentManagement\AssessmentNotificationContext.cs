using Domain.Entities.AssessmentManagement.Enums;

namespace Domain.Entities.AssessmentManagement
{
    /// <summary>
    /// Context class for Assessment notification triggers
    /// Contains all necessary information for generating appropriate notifications
    /// Based on state transitions and user actions in the Assessment workflow
    /// Integrates seamlessly with the Assessment State Pattern
    /// </summary>
    public class AssessmentNotificationContext
    {
        /// <summary>
        /// The action that triggered the notification
        /// </summary>
        public AssessmentActionEnum TriggeredAction { get; set; }

        /// <summary>
        /// The status that was transitioned to
        /// </summary>
        public AssessmentStatus TriggeredStatus { get; set; }

        /// <summary>
        /// ID of the user who performed the action
        /// </summary>
        public int ActorUserId { get; set; }

        /// <summary>
        /// Name of the user who performed the action
        /// </summary>
        public string ActorUserName { get; set; } = string.Empty;

        /// <summary>
        /// Role of the user who performed the action
        /// </summary>
        public string ActorRole { get; set; } = string.Empty;

        /// <summary>
        /// Additional details about the action
        /// </summary>
        public string ActionDetails { get; set; } = string.Empty;

        /// <summary>
        /// Rejection reason if applicable
        /// </summary>
        public string RejectionReason { get; set; } = string.Empty;

        /// <summary>
        /// Optional comments provided with the action
        /// </summary>
        public string? Comments { get; set; }

        /// <summary>
        /// List of specific board member IDs for distribution notifications
        /// Used when distributing to specific board members rather than all
        /// </summary>
        public List<int>? SpecificBoardMemberIds { get; set; }

        /// <summary>
        /// Completion statistics for assessment completion notifications
        /// </summary>
        public AssessmentCompletionStatistics? CompletionStatistics { get; set; }

        /// <summary>
        /// Assessment response information for response submission notifications
        /// </summary>
        public AssessmentResponseNotificationInfo? ResponseInfo { get; set; }

        /// <summary>
        /// Whether to send notification emails
        /// Default is true
        /// </summary>
        public bool SendNotifications { get; set; } = true;

        /// <summary>
        /// Creates a notification context for assessment submission
        /// </summary>
        /// <param name="actorUserId">ID of the user submitting</param>
        /// <param name="actorUserName">Name of the user submitting</param>
        /// <param name="comments">Optional submission comments</param>
        /// <returns>Configured notification context</returns>
        public static AssessmentNotificationContext ForSubmission(int actorUserId, string actorUserName, string? comments = null)
        {
            return new AssessmentNotificationContext
            {
                ActorUserId = actorUserId,
                ActorUserName = actorUserName,
                Comments = comments,
                SendNotifications = true
            };
        }

        /// <summary>
        /// Creates a notification context for assessment approval
        /// </summary>
        /// <param name="actorUserId">ID of the user approving</param>
        /// <param name="actorUserName">Name of the user approving</param>
        /// <param name="actorRole">Role of the user approving</param>
        /// <param name="comments">Optional approval comments</param>
        /// <returns>Configured notification context</returns>
        public static AssessmentNotificationContext ForApproval(int actorUserId, string actorUserName, string actorRole, string? comments = null)
        {
            return new AssessmentNotificationContext
            {
                ActorUserId = actorUserId,
                ActorUserName = actorUserName,
                ActorRole = actorRole,
                Comments = comments,
                SendNotifications = true
            };
        }

        /// <summary>
        /// Creates a notification context for assessment rejection
        /// </summary>
        /// <param name="actorUserId">ID of the user rejecting</param>
        /// <param name="actorUserName">Name of the user rejecting</param>
        /// <param name="actorRole">Role of the user rejecting</param>
        /// <param name="rejectionReason">Reason for rejection</param>
        /// <param name="comments">Optional additional comments</param>
        /// <returns>Configured notification context</returns>
        public static AssessmentNotificationContext ForRejection(int actorUserId, string actorUserName, string actorRole, string rejectionReason, string? comments = null)
        {
            return new AssessmentNotificationContext
            {
                ActorUserId = actorUserId,
                ActorUserName = actorUserName,
                ActorRole = actorRole,
                RejectionReason = rejectionReason,
                Comments = comments,
                SendNotifications = true
            };
        }

        /// <summary>
        /// Creates a notification context for assessment distribution
        /// </summary>
        /// <param name="actorUserId">ID of the user distributing</param>
        /// <param name="actorUserName">Name of the user distributing</param>
        /// <param name="boardMemberIds">List of board member IDs to notify</param>
        /// <param name="comments">Optional distribution comments</param>
        /// <param name="sendNotifications">Whether to send notifications</param>
        /// <returns>Configured notification context</returns>
        public static AssessmentNotificationContext ForDistribution(int actorUserId, string actorUserName, List<int> boardMemberIds, string? comments = null, bool sendNotifications = true)
        {
            return new AssessmentNotificationContext
            {
                ActorUserId = actorUserId,
                ActorUserName = actorUserName,
                SpecificBoardMemberIds = boardMemberIds,
                Comments = comments,
                SendNotifications = sendNotifications
            };
        }

        /// <summary>
        /// Creates a notification context for assessment completion
        /// </summary>
        /// <param name="completionStatistics">Statistics about the completion</param>
        /// <returns>Configured notification context</returns>
        public static AssessmentNotificationContext ForCompletion(AssessmentCompletionStatistics completionStatistics)
        {
            return new AssessmentNotificationContext
            {
                CompletionStatistics = completionStatistics,
                SendNotifications = true
            };
        }

        /// <summary>
        /// Creates a notification context for response submission
        /// </summary>
        /// <param name="responseInfo">Information about the submitted response</param>
        /// <returns>Configured notification context</returns>
        public static AssessmentNotificationContext ForResponseSubmission(AssessmentResponseNotificationInfo responseInfo)
        {
            return new AssessmentNotificationContext
            {
                ResponseInfo = responseInfo,
                SendNotifications = true
            };
        }
    }

    /// <summary>
    /// Information about assessment response for notifications
    /// </summary>
    public class AssessmentResponseNotificationInfo
    {
        /// <summary>
        /// ID of the assessment response
        /// </summary>
        public int ResponseId { get; set; }

        /// <summary>
        /// ID of the user who submitted the response
        /// </summary>
        public int RespondentUserId { get; set; }

        /// <summary>
        /// Name of the user who submitted the response
        /// </summary>
        public string RespondentUserName { get; set; } = string.Empty;

        /// <summary>
        /// Date when the response was submitted
        /// </summary>
        public DateTime SubmittedAt { get; set; }

        /// <summary>
        /// Completion percentage of the response
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Whether this response triggered assessment completion
        /// </summary>
        public bool TriggeredCompletion { get; set; }
    }

    /// <summary>
    /// Statistics for assessment completion notifications
    /// Reused from the notification service interface
    /// </summary>
    public class AssessmentCompletionStatistics
    {
        /// <summary>
        /// Total number of board members assigned
        /// </summary>
        public int TotalAssigned { get; set; }

        /// <summary>
        /// Number of completed responses
        /// </summary>
        public int CompletedResponses { get; set; }

        /// <summary>
        /// Completion percentage
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Average response time in hours
        /// </summary>
        public double? AverageResponseTime { get; set; }

        /// <summary>
        /// Date when assessment was completed
        /// </summary>
        public DateTime CompletedAt { get; set; }

        /// <summary>
        /// Duration from distribution to completion in days
        /// </summary>
        public int? CompletionDurationDays { get; set; }
    }
}
