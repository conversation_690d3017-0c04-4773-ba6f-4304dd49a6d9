using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Question Analysis
    /// Arabic: كائل نقل البيانات لتحليل الأسئلة
    /// </summary>
    public record QuestionAnalysisDto
    {
        /// <summary>
        /// Question ID
        /// Arabic: معرف السؤال
        /// </summary>
        public int QuestionId { get; set; }

        /// <summary>
        /// Question text
        /// Arabic: نص السؤال
        /// </summary>
        public string QuestionText { get; set; } = string.Empty;

        /// <summary>
        /// Question type
        /// Arabic: نوع السؤال
        /// </summary>
        public QuestionType Type { get; set; }

        /// <summary>
        /// Question order
        /// Arabic: ترتيب السؤال
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Number of responses to this question
        /// Arabic: عدد الردود على هذا السؤال
        /// </summary>
        public int ResponseCount { get; set; }

        /// <summary>
        /// Response rate for this question
        /// Arabic: معدل الاستجابة لهذا السؤال
        /// </summary>
        public decimal ResponseRate { get; set; }

        /// <summary>
        /// Option analysis (for choice-type questions)
        /// Arabic: تحليل الخيارات
        /// </summary>
        public List<OptionAnalysisDto>? OptionAnalysis { get; set; }

        /// <summary>
        /// Text responses (for text-type questions)
        /// Arabic: الردود النصية
        /// </summary>
        public List<TextResponseDto>? TextResponses { get; set; }

        /// <summary>
        /// Most common answers/themes
        /// Arabic: الإجابات/المواضيع الأكثر شيوعاً
        /// </summary>
        public List<string>? CommonThemes { get; set; }
    }
}
