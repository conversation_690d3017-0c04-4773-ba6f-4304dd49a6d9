using AutoMapper;

namespace Application.Mapping.AssessmentManagement
{
    /// <summary>
    /// AutoMapper profile for Assessment-related mappings
    /// Contains all mapping configurations for Assessment domain entities and DTOs
    /// Follows the same pattern as ResolutionsProfile for consistency
    /// </summary>
    public partial class AssessmentProfile : Profile
    {
        public AssessmentProfile()
        {
            GetAssessmentMapping();
            CreateAssessmentMapping();
            UpdateAssessmentMapping();
            // Add other mapping methods here as they are created
            // AssessmentResponseMapping();
            // etc.
        }
    }
}
