using Xunit;
using Moq;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Queries.GetById;
using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Domain.Entities.Shared;
using Resources;
using System.Net;

namespace Application.Tests.Features.AssessmentManagement.Queries.GetById
{
    /// <summary>
    /// Unit tests for GetAssessmentByIdQueryHandler
    /// Tests the CQRS query handler for retrieving assessment details by ID
    /// </summary>
    public class GetAssessmentByIdQueryHandlerTests
    {
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly GetAssessmentByIdQueryHandler _handler;

        public GetAssessmentByIdQueryHandlerTests()
        {
            _mockLogger = new Mock<ILoggerManager>();
            _mockRepository = new Mock<IRepositoryManager>();
            _mockMapper = new Mock<IMapper>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();

            _handler = new GetAssessmentByIdQueryHandler(
                _mockLogger.Object,
                _mockRepository.Object,
                _mockMapper.Object,
                _mockLocalizer.Object,
                _mockCurrentUserService.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccessResponse()
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var query = new GetAssessmentByIdQuery(assessmentId);

            var mockUser = new Domain.Entities.Users.User { Id = userId };
            var mockAssessment = new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Description = "Test Description",
                Type = AssessmentType.Questionnaire,
                Status = AssessmentStatus.Active,
                FundId = 1,
                Fund = new Fund { Id = 1, Name = "Test Fund" },
                Questions = new List<AssessmentQuestion>
                {
                    new AssessmentQuestion
                    {
                        Id = 1,
                        QuestionText = "Test Question",
                        QuestionType = QuestionType.MultipleChoice,
                        DisplayOrder = 1,
                        IsRequired = true,
                        Options = new List<Option>
                        {
                            new Option { Id = 1, Value = "Option 1", Order = 1, IsCorrect = false },
                            new Option { Id = 2, Value = "Option 2", Order = 2, IsCorrect = true }
                        }
                    }
                }
            };

            var expectedDto = new AssessmentByIdDto
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Description = "Test Description",
                Type = AssessmentType.Questionnaire,
                FundId = 1,
                FundName = "Test Fund",
                Questions = new List<AssessmentQuestionByIdDto>
                {
                    new AssessmentQuestionByIdDto
                    {
                        Id = 1,
                        QuestionText = "Test Question",
                        QuestionType = QuestionType.MultipleChoice,
                        DisplayOrder = 1,
                        IsRequired = true,
                        Options = new List<AssessmentOptionByIdDto>
                        {
                            new AssessmentOptionByIdDto { Id = 1, Value = "Option 1", Order = 1, IsCorrect = false },
                            new AssessmentOptionByIdDto { Id = 2, Value = "Option 2", Order = 2, IsCorrect = true }
                        }
                    }
                }
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, false))
                .ReturnsAsync(mockAssessment);
            _mockMapper.Setup(x => x.Map<AssessmentByIdDto>(mockAssessment))
                .Returns(expectedDto);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Successed);
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(assessmentId, result.Data.Id);
            Assert.Equal("Test Assessment", result.Data.Title);
            Assert.Equal("Test Fund", result.Data.FundName);
            Assert.Single(result.Data.Questions);
            Assert.Equal(2, result.Data.Questions.First().Options.Count);

            _mockLogger.Verify(x => x.LogInfo($"Getting assessment details for ID: {assessmentId}"), Times.Once);
            _mockLogger.Verify(x => x.LogInfo($"Successfully retrieved assessment details for ID: {assessmentId}"), Times.Once);
        }

        [Fact]
        public async Task Handle_UserNotAuthenticated_ReturnsUnauthorized()
        {
            // Arrange
            var query = new GetAssessmentByIdQuery(1);
            _mockCurrentUserService.Setup(x => x.UserId).Returns((int?)null);
            _mockLocalizer.Setup(x => x["UnauthorizedAccess"]).Returns(new LocalizedString("UnauthorizedAccess", "Unauthorized access"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Successed);
            Assert.Equal(HttpStatusCode.Unauthorized, result.StatusCode);
        }

        [Fact]
        public async Task Handle_AssessmentNotFound_ReturnsNotFound()
        {
            // Arrange
            var assessmentId = 999;
            var userId = 123;
            var query = new GetAssessmentByIdQuery(assessmentId);
            var mockUser = new Domain.Entities.Users.User { Id = userId };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, false))
                .ReturnsAsync((Assessment?)null);
            _mockLocalizer.Setup(x => x["AssessmentNotFound"]).Returns(new LocalizedString("AssessmentNotFound", "Assessment not found"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Successed);
            Assert.Equal(HttpStatusCode.NotFound, result.StatusCode);
        }
    }
}
