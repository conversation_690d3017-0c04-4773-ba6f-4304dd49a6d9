using Abstraction.Contracts.Repository;

namespace Abstraction.Contracts.Repository.Identity
{
    /// <summary>
    /// Repository interface for User entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for user business logic
    /// </summary>
    public interface IUserRepository : IGenericRepository
    {
        // Add specific repository methods if needed
        // Task<User> GetUserWithDetailsAsync(int id, bool trackChanges = false);
    }
}
