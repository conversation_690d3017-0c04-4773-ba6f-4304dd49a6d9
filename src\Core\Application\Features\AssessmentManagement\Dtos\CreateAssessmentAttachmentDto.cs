namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for creating assessment attachments
    /// Arabic: كائل نقل البيانات لإنشاء مرفقات التقييم
    /// </summary>
    public record CreateAssessmentAttachmentDto
    {
        /// <summary>
        /// Attachment file ID
        /// Arabic: معرف الملف المرفق
        /// </summary>
        public int AttachmentId { get; set; }

        /// <summary>
        /// Attachment description
        /// Arabic: وصف المرفق
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Attachment order/sequence
        /// Arabic: ترتيب المرفق
        /// </summary>
        public int Order { get; set; }
    }
}
