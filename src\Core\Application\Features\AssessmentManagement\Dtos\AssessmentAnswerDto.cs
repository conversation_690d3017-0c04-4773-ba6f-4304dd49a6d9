using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Answer
    /// Arabic: كائل نقل البيانات لإجابة التقييم
    /// </summary>
    public record AssessmentAnswerDto : BaseDto
    {
        /// <summary>
        /// Question ID this answer belongs to
        /// Arabic: معرف السؤال
        /// </summary>
        public int QuestionId { get; set; }

        /// <summary>
        /// Question text for display
        /// Arabic: نص السؤال للعرض
        /// </summary>
        public string? QuestionText { get; set; }

        /// <summary>
        /// Question type
        /// Arabic: نوع السؤال
        /// </summary>
        public QuestionType QuestionType { get; set; }

        /// <summary>
        /// Text answer (for text-type questions)
        /// Arabic: الإجابة النصية
        /// </summary>
        public string? TextAnswer { get; set; }

        /// <summary>
        /// Selected options (for choice-type questions)
        /// Arabic: الخيارات المختارة
        /// </summary>
        public List<AssessmentAnswerOptionDto>? SelectedOptions { get; set; }

        /// <summary>
        /// Whether the answer is complete
        /// Arabic: الإجابة مكتملة
        /// </summary>
        public bool IsComplete { get; set; }

        /// <summary>
        /// Answer submission date
        /// Arabic: تاريخ إرسال الإجابة
        /// </summary>
        public DateTime? AnsweredAt { get; set; }
    }
}
