using Application.Contracts.Services;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.Notifications;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.FundManagement;

namespace Infrastructure.Service.Notifications
{
    /// <summary>
    /// Implementation of Assessment notification service
    /// Handles all notification scenarios for the Assessment Management module
    /// Based on AssessmentStories.md requirements and follows the established notification patterns
    /// Integrates with the existing notification infrastructure
    /// </summary>
    public class AssessmentNotificationService : IAssessmentNotificationService
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructor
        public AssessmentNotificationService(
            ILoggerManager logger,
            IRepositoryManager repository,
            IStringLocalizer<SharedResources> localizer)
        {
            _logger = logger;
            _repository = repository;
            _localizer = localizer;
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Sends notification when assessment is submitted for approval
        /// Based on MSG-ASM-002 from AssessmentStories.md
        /// </summary>
        public async Task SendAssessmentSubmittedForApprovalNotificationAsync(
            Assessment assessment, 
            int creatorUserId, 
            string creatorUserName, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment submitted for approval notifications for Assessment ID: {assessment.Id}");

                var notifications = new List<Notification>();
                var fund = await _repository.Funds.ViewFundUsers(assessment.FundId,false);
                
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                // Notify Legal Council
                if (fund.LegalCouncilId > 0)
                {
                    notifications.Add(new Notification
                    {
                        Title = string.Empty,
                        Body = $"{assessment.Title}|{fund.Name}|{creatorUserName}",
                        FundId = fund.Id,
                        UserId = fund.LegalCouncilId,
                        NotificationType = (int)NotificationType.AssessmentCreated,
                        NotificationModule = (int)NotificationModule.Evaluations,
                        IsRead = false,
                        IsSent = false,
                        CreatedBy = creatorUserId,
                        CreatedAt = DateTime.Now
                    });
                }

                // Notify Board Secretaries
                var boardSecretaries = fund.FundBoardSecretaries ?? new List<Domain.Entities.FundManagement.FundBoardSecretary>();
                foreach (var boardSecretary in boardSecretaries)
                {
                    notifications.Add(new Notification
                    {
                        Title = string.Empty,
                        Body = $"{assessment.Title}|{fund.Name}|{creatorUserName}",
                        FundId = fund.Id,
                        UserId = boardSecretary.UserId,
                        NotificationType = (int)NotificationType.AssessmentCreated,
                        NotificationModule = (int)NotificationModule.Evaluations,
                        IsRead = false,
                        IsSent = false,
                        CreatedBy = creatorUserId,
                        CreatedAt = DateTime.Now
                    });
                }

                if (notifications.Any())
                {
                    await _repository.Notifications.AddRangeAsync(notifications);
                    _logger.LogInfo($"Assessment submitted for approval notifications sent. Count: {notifications.Count}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,$"Error sending assessment submitted for approval notifications: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        /// <summary>
        /// Sends notification when assessment is approved
        /// Based on MSG-ASM-005 from AssessmentStories.md
        /// </summary>
        public async Task SendAssessmentApprovedNotificationAsync(
            Assessment assessment, 
            int approverUserId, 
            string approverUserName, 
            string approverRole, 
            string? comments = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment approved notification for Assessment ID: {assessment.Id}");

                var fund = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId,false);
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                // Notify the assessment creator (Fund Manager)
                var notification = new Notification
                {
                    Title = string.Empty,
                    Body = $"{assessment.Title}|{fund.Name}|{approverUserName}|{approverRole}|{comments ?? ""}",
                    FundId = fund.Id,
                    UserId = assessment.CreatedBy,
                    NotificationType = (int)NotificationType.AssessmentApproved,
                    NotificationModule = (int)NotificationModule.Evaluations,
                    IsRead = false,
                    IsSent = false,
                    CreatedBy = approverUserId,
                    CreatedAt = DateTime.Now
                };

                await _repository.Notifications.AddAsync(notification);
                _logger.LogInfo($"Assessment approved notification sent to user: {assessment.CreatedBy}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,$"Error sending assessment approved notification: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        /// <summary>
        /// Sends notification when assessment is rejected
        /// Based on MSG-ASM-006 from AssessmentStories.md
        /// </summary>
        public async Task SendAssessmentRejectedNotificationAsync(
            Assessment assessment, 
            int rejecterUserId, 
            string rejecterUserName, 
            string rejecterRole, 
            string rejectionReason, 
            string? comments = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment rejected notification for Assessment ID: {assessment.Id}");

                var fund = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, false);
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                // Notify the assessment creator (Fund Manager) with rejection reason
                var notification = new Notification
                {
                    Title = string.Empty,
                    Body = $"{assessment.Title}|{fund.Name}|{rejecterUserName}|{rejecterRole}|{rejectionReason}|{comments ?? ""}",
                    FundId = fund.Id,
                    UserId = assessment.CreatedBy,
                    NotificationType = (int)NotificationType.AssessmentRejected,
                    NotificationModule = (int)NotificationModule.Evaluations,
                    IsRead = false,
                    IsSent = false,
                    CreatedBy = rejecterUserId,
                    CreatedAt = DateTime.Now
                };

                await _repository.Notifications.AddAsync(notification);
                _logger.LogInfo($"Assessment rejected notification sent to user: {assessment.CreatedBy}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,$"Error sending assessment rejected notification: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        /// <summary>
        /// Sends notification when assessment is distributed to board members
        /// Based on MSG-DIST-002 from AssessmentStories.md
        /// </summary>
        public async Task SendAssessmentDistributedNotificationAsync(
            Assessment assessment, 
            int distributorUserId, 
            string distributorUserName, 
            List<int> boardMemberIds, 
            string? comments = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment distributed notifications for Assessment ID: {assessment.Id} to {boardMemberIds.Count} board members");

                var notifications = new List<Notification>();
                var fund = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, false);
                
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                // Create notification body with assessment details
                var dueDateText = assessment.DueDate?.ToString("yyyy-MM-dd") ?? "";
                var notificationBody = $"{assessment.Title}|{fund.Name}|{distributorUserName}|{dueDateText}|{comments ?? ""}";

                // Notify each board member
                foreach (var boardMemberId in boardMemberIds)
                {
                    notifications.Add(new Notification
                    {
                        Title = string.Empty,
                        Body = notificationBody,
                        FundId = fund.Id,
                        UserId = boardMemberId,
                        NotificationType = (int)NotificationType.AssessmentDistributed,
                        NotificationModule = (int)NotificationModule.Evaluations,
                        IsRead = false,
                        IsSent = false,
                        CreatedBy = distributorUserId,
                        CreatedAt = DateTime.Now
                    });
                }

                if (notifications.Any())
                {
                    await _repository.Notifications.AddRangeAsync(notifications);
                    _logger.LogInfo($"Assessment distributed notifications sent. Count: {notifications.Count}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending assessment distributed notifications: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        /// <summary>
        /// Sends notification when assessment is completed (all responses collected)
        /// </summary>
        public async Task SendAssessmentCompletedNotificationAsync(
            Assessment assessment, 
            AssessmentCompletionStatistics completionStatistics, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment completed notification for Assessment ID: {assessment.Id}");

                var notifications = new List<Notification>();
                var fund = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, false);
                
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                var notificationBody = $"{assessment.Title}|{fund.Name}|{completionStatistics.CompletedResponses}|{completionStatistics.TotalAssigned}|{completionStatistics.CompletionPercentage:F1}%";

                // Notify Fund Manager (assessment creator)
                notifications.Add(new Notification
                {
                    Title = string.Empty,
                    Body = notificationBody,
                    FundId = fund.Id,
                    UserId = assessment.CreatedBy,
                    NotificationType = (int)NotificationType.AssessmentCompleted,
                    NotificationModule = (int)NotificationModule.Evaluations,
                    IsRead = false,
                    IsSent = false,
                    CreatedBy = 0, // System generated
                    CreatedAt = DateTime.Now
                });

                // Notify Legal Council
                if (fund.LegalCouncilId > 0)
                {
                    notifications.Add(new Notification
                    {
                        Title = string.Empty,
                        Body = notificationBody,
                        FundId = fund.Id,
                        UserId = fund.LegalCouncilId,
                        NotificationType = (int)NotificationType.AssessmentCompleted,
                        NotificationModule = (int)NotificationModule.Evaluations,
                        IsRead = false,
                        IsSent = false,
                        CreatedBy = 0, // System generated
                        CreatedAt = DateTime.Now
                    });
                }

                if (notifications.Any())
                {
                    await _repository.Notifications.AddRangeAsync(notifications);
                    _logger.LogInfo($"Assessment completed notifications sent. Count: {notifications.Count}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending assessment completed notifications: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        /// <summary>
        /// Sends notification when a board member submits their response
        /// </summary>
        public async Task SendAssessmentResponseSubmittedNotificationAsync(
            Assessment assessment, 
            AssessmentResponse response, 
            string respondentUserName, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment response submitted notification for Assessment ID: {assessment.Id}");

                var fund = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, false);
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                // Notify Fund Manager (assessment creator)
                var notification = new Notification
                {
                    Title = string.Empty,
                    Body = $"{assessment.Title}|{fund.Name}|{respondentUserName}|{response.CreatedAt:yyyy-MM-dd HH:mm}",
                    FundId = fund.Id,
                    UserId = assessment.CreatedBy,
                    NotificationType = (int)NotificationType.AssessmentResponseSubmitted,
                    NotificationModule = (int)NotificationModule.Evaluations,
                    IsRead = false,
                    IsSent = false,
                    CreatedBy = response.UserId,
                    CreatedAt = DateTime.Now
                };

                await _repository.Notifications.AddAsync(notification);
                _logger.LogInfo($"Assessment response submitted notification sent to user: {assessment.CreatedBy}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending assessment response submitted notification: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        /// <summary>
        /// Sends reminder notification to board members who haven't responded
        /// </summary>
        public async Task SendAssessmentReminderNotificationAsync(
            Assessment assessment, 
            List<int> pendingBoardMemberIds, 
            int? daysRemaining = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Sending assessment reminder notifications for Assessment ID: {assessment.Id} to {pendingBoardMemberIds.Count} board members");

                var notifications = new List<Notification>();
                var fund = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, false);
                
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found for Assessment ID: {assessment.Id}");
                    return;
                }

                var dueDateText = assessment.DueDate?.ToString("yyyy-MM-dd") ?? "";
                var daysRemainingText = daysRemaining?.ToString() ?? "";
                var notificationBody = $"{assessment.Title}|{fund.Name}|{dueDateText}|{daysRemainingText}";

                // Send reminder to each pending board member
                foreach (var boardMemberId in pendingBoardMemberIds)
                {
                    notifications.Add(new Notification
                    {
                        Title = string.Empty,
                        Body = notificationBody,
                        FundId = fund.Id,
                        UserId = boardMemberId,
                        NotificationType = (int)NotificationType.AssessmentReminder,
                        NotificationModule = (int)NotificationModule.Evaluations,
                        IsRead = false,
                        IsSent = false,
                        CreatedBy = 0, // System generated
                        CreatedAt = DateTime.Now
                    });
                }

                if (notifications.Any())
                {
                    await _repository.Notifications.AddRangeAsync(notifications);
                    _logger.LogInfo($"Assessment reminder notifications sent. Count: {notifications.Count}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending assessment reminder notifications: {ex.Message}");
                // Don't throw - notification failures shouldn't break the main flow
            }
        }

        #endregion
    }
}
