using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Represents an assessment waiting for approval
    /// Can transition to Approved or Rejected states
    /// Does not allow editing or deletion
    /// Based on User Story 2: Approve or Reject Assessment
    /// </summary>
    public class WaitingForApprovalAssessmentState : IAssessmentState
    {
        public AssessmentStatus Status => AssessmentStatus.WaitingForApproval;

        public void Handle(Assessment assessment)
        {
            // Waiting for approval state - assessment is read-only and awaiting review
            // No specific handling required
        }

        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return targetStatus == AssessmentStatus.Approved ||
                   targetStatus == AssessmentStatus.Rejected;
        }

        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return new[]
            {
                AssessmentStatus.Approved,
                AssessmentStatus.Rejected
            };
        }

        public bool CanEdit()
        {
            return false; // Assessments waiting for approval cannot be edited
        }

        public bool CanDelete()
        {
            return false; // Assessments waiting for approval cannot be deleted
        }

        public bool CanSubmitForApproval()
        {
            return false; // Already submitted for approval
        }

        public bool CanApprove()
        {
            return true; // Assessments waiting for approval can be approved
        }

        public bool CanReject()
        {
            return true; // Assessments waiting for approval can be rejected
        }

        public bool CanDistribute()
        {
            return false; // Must be approved first before distribution
        }

        public bool CanReceiveResponses()
        {
            return false; // Must be active to receive responses
        }

        public bool CanViewResults()
        {
            return false; // No results available until assessment is active
        }

        public bool CanMarkAsCompleted(Assessment assessment)
        {
            return false; // Cannot be completed until distributed and responses collected
        }

        public IEnumerable<string> GetValidationMessages()
        {
            return new[]
            {
                "Assessment is waiting for approval and cannot be edited or deleted",
                "Assessment can be approved or rejected by Legal Council or Board Secretary"
            };
        }

        public string GetStateDescriptionKey()
        {
            return "AssessmentInWaitingForApprovalState";
        }

        public string GetStateDisplayNameKey()
        {
            return "AssessmentStatusWaitingForApproval";
        }
    }
}
