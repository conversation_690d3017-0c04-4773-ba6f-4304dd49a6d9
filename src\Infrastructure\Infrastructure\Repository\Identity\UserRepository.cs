using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.Identity;
using Infrastructure.Data;

namespace Infrastructure.Repository.Identity
{
    /// <summary>
    /// Repository implementation for User entity operations
    /// Provides data access functionality using Entity Framework Core
    /// </summary>
    public class UserRepository : GenericRepository, IUserRepository
    {
        public UserRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService) 
            : base(repositoryContext, currentUserService)
        {
        }

        // Implement specific repository methods if needed
        // public async Task<User> GetUserWithDetailsAsync(int id, bool trackChanges = false)
        // {
        //     return await GetByCondition<User>(x => x.Id == id, trackChanges)
        //         .Include(u => u.Roles)
        //         .FirstOrDefaultAsync();
        // }
    }
}
