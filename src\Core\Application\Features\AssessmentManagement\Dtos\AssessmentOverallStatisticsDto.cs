namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Overall Assessment Statistics
    /// Arabic: كائل نقل البيانات للإحصائيات العامة للتقييم
    /// </summary>
    public record AssessmentOverallStatisticsDto
    {
        /// <summary>
        /// Total number of assigned board members
        /// Arabic: العدد الإجمالي لأعضاء مجلس الإدارة المكلفين
        /// </summary>
        public int TotalAssigned { get; set; }

        /// <summary>
        /// Number of completed responses
        /// Arabic: عدد الردود المكتملة
        /// </summary>
        public int CompletedResponses { get; set; }

        /// <summary>
        /// Number of pending responses
        /// Arabic: عدد الردود المعلقة
        /// </summary>
        public int PendingResponses { get; set; }

        /// <summary>
        /// Response completion percentage
        /// Arabic: نسبة إكمال الردود
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Average response time in hours
        /// Arabic: متوسط وقت الاستجابة بالساعات
        /// </summary>
        public double? AverageResponseTime { get; set; }

        /// <summary>
        /// Fastest response time in hours
        /// Arabic: أسرع وقت استجابة بالساعات
        /// </summary>
        public double? FastestResponseTime { get; set; }

        /// <summary>
        /// Slowest response time in hours
        /// Arabic: أبطأ وقت استجابة بالساعات
        /// </summary>
        public double? SlowestResponseTime { get; set; }

        /// <summary>
        /// Total number of questions (for questionnaire type)
        /// Arabic: العدد الإجمالي للأسئلة
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// Average completion percentage across all responses
        /// Arabic: متوسط نسبة الإكمال عبر جميع الردود
        /// </summary>
        public decimal AverageCompletionPercentage { get; set; }
    }
}
