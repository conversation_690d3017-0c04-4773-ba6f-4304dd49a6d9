using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Queries.GetById
{
    /// <summary>
    /// Query to get assessment details by ID
    /// Follows CQRS pattern for read operations
    /// Based on API specification requirements for GET /api/assessments/{id}
    /// Arabic: استعلام للحصول على تفاصيل التقييم حسب المعرف
    /// </summary>
    public record GetAssessmentByIdQuery : IQuery<BaseResponse<AssessmentByIdDto>>
    {
        /// <summary>
        /// Assessment ID to retrieve
        /// Arabic: معرف التقييم المراد استرجاعه
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Constructor for GetAssessmentByIdQuery
        /// </summary>
        /// <param name="id">Assessment ID</param>
        public GetAssessmentByIdQuery(int id)
        {
            Id = id;
        }

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetAssessmentByIdQuery() { }
    }
}
