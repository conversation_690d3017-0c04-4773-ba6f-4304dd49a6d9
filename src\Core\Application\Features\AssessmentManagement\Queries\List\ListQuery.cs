using Abstraction.Base.Dto;
using Abstraction.Common.Wappers;
using Application.Base.Abstracts;
using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Queries.List
{
    /// <summary>
    /// Query for listing assessments with filtering and pagination
    /// Based on Assessment requirements and user stories
    /// Supports role-based filtering and comprehensive search criteria
    /// Follows the same pattern as Resolution ListQuery for consistency
    /// </summary>
    public record ListQuery : BaseListDto, IQuery<PaginatedResult<SingleAssessmentResponse>>
    {
        /// <summary>
        /// Filter by fund ID (optional)
        /// </summary>
        public int? FundId { get; set; }

        /// <summary>
        /// Filter by assessment status (optional)
        /// </summary>
        public AssessmentStatus? Status { get; set; }

        /// <summary>
        /// Filter by assessment type (optional)
        /// </summary>
        public AssessmentType? Type { get; set; }

        /// <summary>
        /// Filter by date range - start date (optional)
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Filter by date range - end date (optional)
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Filter by created by user ID (optional)
        /// </summary>
        public int? CreatedBy { get; set; }
    }
}
