using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Contracts.Services
{
    /// <summary>
    /// Interface for Assessment notification service
    /// Handles all notification scenarios for the Assessment Management module
    /// Based on AssessmentStories.md requirements and follows the established notification patterns
    /// Integrates with the Assessment State Pattern for seamless notification triggers
    /// </summary>
    public interface IAssessmentNotificationService
    {
        /// <summary>
        /// Sends notification when assessment is submitted for approval
        /// Based on MSG-ASM-002 from AssessmentStories.md
        /// Notifies Legal Council and Board Secretary
        /// </summary>
        /// <param name="assessment">Assessment that was submitted</param>
        /// <param name="creatorUserId">ID of the user who created the assessment</param>
        /// <param name="creatorUserName">Name of the user who created the assessment</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentSubmittedForApprovalNotificationAsync(
            Assessment assessment, 
            int creatorUserId, 
            string creatorUserName, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends notification when assessment is approved
        /// Based on MSG-ASM-005 from AssessmentStories.md
        /// Notifies the Fund Manager who created the assessment
        /// </summary>
        /// <param name="assessment">Assessment that was approved</param>
        /// <param name="approverUserId">ID of the user who approved the assessment</param>
        /// <param name="approverUserName">Name of the user who approved the assessment</param>
        /// <param name="approverRole">Role of the user who approved the assessment</param>
        /// <param name="comments">Optional approval comments</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentApprovedNotificationAsync(
            Assessment assessment, 
            int approverUserId, 
            string approverUserName, 
            string approverRole, 
            string? comments = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends notification when assessment is rejected
        /// Based on MSG-ASM-006 from AssessmentStories.md
        /// Notifies the Fund Manager who created the assessment with rejection reason
        /// </summary>
        /// <param name="assessment">Assessment that was rejected</param>
        /// <param name="rejecterUserId">ID of the user who rejected the assessment</param>
        /// <param name="rejecterUserName">Name of the user who rejected the assessment</param>
        /// <param name="rejecterRole">Role of the user who rejected the assessment</param>
        /// <param name="rejectionReason">Reason for rejection</param>
        /// <param name="comments">Optional additional comments</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentRejectedNotificationAsync(
            Assessment assessment, 
            int rejecterUserId, 
            string rejecterUserName, 
            string rejecterRole, 
            string rejectionReason, 
            string? comments = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends notification when assessment is distributed to board members
        /// Based on MSG-DIST-002 from AssessmentStories.md
        /// Notifies all assigned board members about the new assessment
        /// </summary>
        /// <param name="assessment">Assessment that was distributed</param>
        /// <param name="distributorUserId">ID of the user who distributed the assessment</param>
        /// <param name="distributorUserName">Name of the user who distributed the assessment</param>
        /// <param name="boardMemberIds">List of board member IDs to notify</param>
        /// <param name="comments">Optional distribution comments</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentDistributedNotificationAsync(
            Assessment assessment, 
            int distributorUserId, 
            string distributorUserName, 
            List<int> boardMemberIds, 
            string? comments = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends notification when assessment is completed (all responses collected)
        /// Notifies Fund Manager and Legal Council with completion summary
        /// </summary>
        /// <param name="assessment">Assessment that was completed</param>
        /// <param name="completionStatistics">Statistics about the completion</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentCompletedNotificationAsync(
            Assessment assessment, 
            AssessmentCompletionStatistics completionStatistics, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends notification when a board member submits their response
        /// Notifies Fund Manager about individual response submission
        /// </summary>
        /// <param name="assessment">Assessment for which response was submitted</param>
        /// <param name="response">Assessment response that was submitted</param>
        /// <param name="respondentUserName">Name of the user who submitted the response</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentResponseSubmittedNotificationAsync(
            Assessment assessment, 
            AssessmentResponse response, 
            string respondentUserName, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends reminder notification to board members who haven't responded
        /// Used for automated reminder systems
        /// </summary>
        /// <param name="assessment">Assessment for which to send reminders</param>
        /// <param name="pendingBoardMemberIds">List of board member IDs who haven't responded</param>
        /// <param name="daysRemaining">Days remaining until due date (if applicable)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SendAssessmentReminderNotificationAsync(
            Assessment assessment, 
            List<int> pendingBoardMemberIds, 
            int? daysRemaining = null, 
            CancellationToken cancellationToken = default);
    }
}
