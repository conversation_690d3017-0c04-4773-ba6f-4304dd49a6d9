namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for personal assessment response view (User Story 6)
    /// Arabic: كائل نقل البيانات لعرض رد التقييم الشخصي
    /// </summary>
    public record MyAssessmentResponseDto : AssessmentResponseDto
    {
        /// <summary>
        /// Assessment details for context
        /// Arabic: تفاصيل التقييم للسياق
        /// </summary>
        public MyAssessmentDto? Assessment { get; set; }

        /// <summary>
        /// Whether this is a draft response
        /// Arabic: هذا رد مسودة
        /// </summary>
        public bool IsDraft { get; set; }

        /// <summary>
        /// Number of times the response has been edited
        /// Arabic: عدد مرات تعديل الرد
        /// </summary>
        public int EditCount { get; set; }

        /// <summary>
        /// Last edit date
        /// Arabic: تاريخ آخر تعديل
        /// </summary>
        public DateTime? LastEditedAt { get; set; }
    }
}
