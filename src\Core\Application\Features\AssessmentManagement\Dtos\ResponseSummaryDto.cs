using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Response Summary
    /// Arabic: كائل نقل البيانات لملخص الرد
    /// </summary>
    public record ResponseSummaryDto
    {
        /// <summary>
        /// Response ID
        /// Arabic: معرف الرد
        /// </summary>
        public int ResponseId { get; set; }

        /// <summary>
        /// Respondent name (if not anonymous)
        /// Arabic: اسم المجيب
        /// </summary>
        public string? RespondentName { get; set; }

        /// <summary>
        /// Response status
        /// Arabic: حالة الرد
        /// </summary>
        public ResponseStatus Status { get; set; }

        /// <summary>
        /// Response completion percentage
        /// Arabic: نسبة إكمال الرد
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Response submission date
        /// Arabic: تاريخ إرسال الرد
        /// </summary>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Time taken to complete response in hours
        /// Arabic: الوقت المستغرق لإكمال الرد بالساعات
        /// </summary>
        public double? TimeTakenHours { get; set; }

        /// <summary>
        /// Number of questions answered
        /// Arabic: عدد الأسئلة المجاب عليها
        /// </summary>
        public int QuestionsAnswered { get; set; }

        /// <summary>
        /// Total number of questions
        /// Arabic: العدد الإجمالي للأسئلة
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// Score (if applicable)
        /// Arabic: النتيجة
        /// </summary>
        public decimal? Score { get; set; }

        /// <summary>
        /// Maximum possible score
        /// Arabic: أقصى نتيجة ممكنة
        /// </summary>
        public decimal? MaxScore { get; set; }
    }
}
