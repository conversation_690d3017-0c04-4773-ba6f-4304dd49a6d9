namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Completion Analytics
    /// Arabic: كائل نقل البيانات لتحليلات الإكمال
    /// </summary>
    public record CompletionAnalyticsDto
    {
        /// <summary>
        /// Completion rate by day of week
        /// Arabic: معدل الإكمال حسب يوم الأسبوع
        /// </summary>
        public Dictionary<string, decimal> CompletionByDayOfWeek { get; set; } = new();

        /// <summary>
        /// Completion rate by time of day
        /// Arabic: معدل الإكمال حسب وقت اليوم
        /// </summary>
        public Dictionary<string, decimal> CompletionByTimeOfDay { get; set; } = new();

        /// <summary>
        /// Average time to first response in hours
        /// Arabic: متوسط الوقت للرد الأول بالساعات
        /// </summary>
        public double? AverageTimeToFirstResponse { get; set; }

        /// <summary>
        /// Response patterns and insights
        /// Arabic: أنماط الردود والرؤى
        /// </summary>
        public List<string> Insights { get; set; } = new();

        /// <summary>
        /// Recommendations for future assessments
        /// Arabic: توصيات للتقييمات المستقبلية
        /// </summary>
        public List<string> Recommendations { get; set; } = new();
    }
}
