using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Commands.CreateAssessment;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for CreateAssessmentCommand
    /// Based on User Story 1: Create New Assessment requirements
    /// Implements comprehensive validation for assessment creation
    /// Follows the same pattern as AddResolutionValidation for consistency
    /// </summary>
    public class CreateAssessmentValidation : BaseAssessmentValidation<CreateAssessmentCommand>
    {
        public CreateAssessmentValidation(IStringLocalizer<SharedResources> localizer) : base(localizer)
        {
            // Validate basic assessment properties
            //ValidateAssessmentTitle(x => x.Title);
            //ValidateAssessmentDescription(x => x.Description);
            //ValidateAssessmentInstructions(x => x.Instructions);
            //ValidateAssessmentType(x => x.Type);
            //ValidateFundId(x => x.FundId);
            //ValidateDueDate(x => x.DueDate);

            //// Validate questions based on assessment type
            //ValidateAssessmentQuestions(x => x.Questions, x => x.Type);

            //// Validate attachments based on assessment type
            //ValidateAssessmentAttachments(x => x.Attachments, x => x.Type);
        }
    }
}
