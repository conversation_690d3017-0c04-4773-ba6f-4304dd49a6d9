using Xunit;
using Moq;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Queries.GetAssessmentDetails;
using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Domain.Entities.Shared;
using Resources;
using System.Net;

namespace Application.Tests.Features.AssessmentManagement.Queries.GetAssessmentDetails
{
    /// <summary>
    /// Unit tests for GetAssessmentDetailsQueryHandler
    /// Tests the CQRS query handler for retrieving comprehensive assessment details
    /// </summary>
    public class GetAssessmentDetailsQueryHandlerTests
    {
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly GetAssessmentDetailsQueryHandler _handler;

        public GetAssessmentDetailsQueryHandlerTests()
        {
            _mockLogger = new Mock<ILoggerManager>();
            _mockRepository = new Mock<IRepositoryManager>();
            _mockMapper = new Mock<IMapper>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();

            _handler = new GetAssessmentDetailsQueryHandler(
                _mockLogger.Object,
                _mockRepository.Object,
                _mockMapper.Object,
                _mockLocalizer.Object,
                _mockCurrentUserService.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccessResponse()
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var query = new GetAssessmentDetailsQuery(assessmentId);

            var mockUser = new Domain.Entities.Users.User { Id = userId };
            var mockFund = new Fund { Id = 1, Name = "Test Fund" };
            var mockAssessment = new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Description = "Test Description",
                Type = AssessmentType.Questionnaire,
                Status = AssessmentStatus.Active,
                FundId = 1,
                Fund = mockFund,
                Questions = new List<AssessmentQuestion>
                {
                    new AssessmentQuestion
                    {
                        Id = 1,
                        QuestionText = "Test Question",
                        QuestionType = QuestionType.SingleChoice,
                        DisplayOrder = 1,
                        IsRequired = true,
                        Options = new List<Option>
                        {
                            new Option { Id = 1, Value = "Option 1", Order = 1, IsCorrect = true },
                            new Option { Id = 2, Value = "Option 2", Order = 2, IsCorrect = false }
                        }
                    }
                },
                Responses = new List<AssessmentResponse>
                {
                    new AssessmentResponse
                    {
                        Id = 1,
                        UserId = userId,
                        Status = ResponseStatus.Completed,
                        SubmittedAt = DateTime.UtcNow,
                        Answers = new List<Answer>
                        {
                            new Answer
                            {
                                Id = 1,
                                QuestionId = 1,
                                TextAnswer = null,
                                SelectedOptions = new List<AnswerOption>
                                {
                                    new AnswerOption { Id = 1, OptionId = 1 }
                                }
                            }
                        }
                    }
                }
            };

            var mockComprehensiveDto = new ComprehensiveAssessmentDto
            {
                Id = assessmentId,
                Title = "Test Assessment",
                FundName = "Test Fund",
                Questions = new List<ComprehensiveQuestionDto>
                {
                    new ComprehensiveQuestionDto
                    {
                        Id = 1,
                        QuestionText = "Test Question",
                        QuestionType = QuestionType.SingleChoice,
                        Options = new List<ComprehensiveOptionDto>
                        {
                            new ComprehensiveOptionDto { Id = 1, Value = "Option 1", Order = 1, IsCorrect = true }
                        }
                    }
                },
                Responses = new List<ComprehensiveResponseDto>
                {
                    new ComprehensiveResponseDto
                    {
                        Id = 1,
                        UserId = userId,
                        Status = ResponseStatus.Completed,
                        Answers = new List<ComprehensiveAnswerDto>
                        {
                            new ComprehensiveAnswerDto
                            {
                                Id = 1,
                                QuestionId = 1,
                                SelectedOptions = new List<ComprehensiveAnswerOptionDto>
                                {
                                    new ComprehensiveAnswerOptionDto { Id = 1, OptionId = 1, OptionText = "Option 1", IsCorrect = true }
                                }
                            }
                        }
                    }
                }
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithComprehensiveDetailsAsync(assessmentId, false))
                .ReturnsAsync(mockAssessment);
            _mockMapper.Setup(x => x.Map<ComprehensiveAssessmentDto>(mockAssessment))
                .Returns(mockComprehensiveDto);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Succeeded);
            Assert.NotNull(result.Data);
            Assert.Equal(assessmentId, result.Data.Id);
            Assert.Equal("Test Assessment", result.Data.Title);
            Assert.Equal("Test Fund", result.Data.FundName);
            Assert.Single(result.Data.Questions);
            Assert.Single(result.Data.Responses);
            Assert.NotNull(result.Data.Statistics);
        }

        [Fact]
        public async Task Handle_AssessmentNotFound_ReturnsNotFound()
        {
            // Arrange
            var assessmentId = 999;
            var userId = 123;
            var query = new GetAssessmentDetailsQuery(assessmentId);
            var mockUser = new Domain.Entities.Users.User { Id = userId };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithComprehensiveDetailsAsync(assessmentId, false))
                .ReturnsAsync((Assessment?)null);
            _mockLocalizer.Setup(x => x["AssessmentNotFound"]).Returns(new LocalizedString("AssessmentNotFound", "Assessment not found"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Succeeded);
            Assert.Equal(HttpStatusCode.NotFound, result.StatusCode);
        }

        [Fact]
        public async Task Handle_UnauthorizedUser_ReturnsUnauthorized()
        {
            // Arrange
            var assessmentId = 1;
            var query = new GetAssessmentDetailsQuery(assessmentId);

            _mockCurrentUserService.Setup(x => x.UserId).Returns((int?)null);
            _mockLocalizer.Setup(x => x["UnauthorizedAccess"]).Returns(new LocalizedString("UnauthorizedAccess", "Unauthorized access"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Succeeded);
            Assert.Equal(HttpStatusCode.Unauthorized, result.StatusCode);
        }
    }
}
