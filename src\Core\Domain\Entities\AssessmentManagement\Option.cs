using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.AssessmentManagement
{
    /// <summary>
    /// Represents an individual answer to a specific question within an assessment response
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Properties are defined based on requirements in AssessmentStories.md
    /// </summary>
    public class Option : FullAuditedEntity
    {
        /// <summary>
        /// Foreign key reference to the AssessmentQuestion this option is for
        /// Required field as specified in user stories
        /// </summary>
        public int QuestionId { get; set; }

        /// <summary>
        /// The optional value
        /// Maximum 4000 characters as specified in user stories
        /// </summary>
        public string? Value { get; set; }
        public int Order { get; set; }
        public bool IsCorrect { get; set; }

        /// <summary>
        /// Navigation property to the AssessmentQuestion this answer is for
        /// Provides access to question information
        /// </summary>
        [ForeignKey("QuestionId")]
        public virtual AssessmentQuestion Question { get; set; } = null!;
    }
}
