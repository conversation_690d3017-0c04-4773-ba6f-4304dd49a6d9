using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Context class for managing assessment state transitions
    /// Implements the State Pattern context for assessment lifecycle management
    /// Based on AssessmentStories.md requirements and Clean Architecture principles
    /// Follows the same pattern as ResolutionStateContext for consistency
    /// </summary>
    public class AssessmentStateContext
    {
        private IAssessmentState _currentState;
        private readonly Assessment _assessment;

        /// <summary>
        /// Initializes a new instance of AssessmentStateContext
        /// </summary>
        /// <param name="assessment">The assessment entity to manage</param>
        public AssessmentStateContext(Assessment assessment)
        {
            _assessment = assessment ?? throw new ArgumentNullException(nameof(assessment));
            _currentState = AssessmentStateFactory.CreateState(assessment.Status);
        }

        /// <summary>
        /// Gets the current state
        /// </summary>
        public IAssessmentState CurrentState => _currentState;

        /// <summary>
        /// Transitions to a new state with validation
        /// </summary>
        /// <param name="targetStatus">Target status to transition to</param>
        /// <param name="action">Action being performed</param>
        /// <param name="reason">Reason for the transition (for audit trail)</param>
        /// <returns>True if transition was successful, false if not allowed</returns>
        public bool TransitionTo(AssessmentStatus targetStatus, AssessmentActionEnum action, string reason = "")
        {
            if (!_currentState.CanTransitionTo(targetStatus))
            {
                return false;
            }

            // Update the assessment status
            _assessment.Status = targetStatus;

            // Create new state instance
            _currentState = AssessmentStateFactory.CreateState(targetStatus);

            // Add status history entry if needed
            AddStatusHistoryEntry(targetStatus, action, reason);

            return true;
        }

        /// <summary>
        /// Validates if a transition is allowed
        /// </summary>
        /// <param name="targetStatus">Target status to validate</param>
        /// <returns>True if transition is allowed</returns>
        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return _currentState.CanTransitionTo(targetStatus);
        }

        /// <summary>
        /// Gets all allowed transitions from current state
        /// </summary>
        /// <returns>Collection of allowed target statuses</returns>
        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return _currentState.GetAllowedTransitions();
        }

        /// <summary>
        /// Validates if editing is allowed in current state
        /// </summary>
        /// <returns>True if editing is allowed</returns>
        public bool CanEdit()
        {
            return _currentState.CanEdit();
        }

        /// <summary>
        /// Validates if deletion is allowed in current state
        /// </summary>
        /// <returns>True if deletion is allowed</returns>
        public bool CanDelete()
        {
            return _currentState.CanDelete();
        }

        /// <summary>
        /// Validates if submission for approval is allowed in current state
        /// </summary>
        /// <returns>True if submission is allowed</returns>
        public bool CanSubmitForApproval()
        {
            return _currentState.CanSubmitForApproval();
        }

        /// <summary>
        /// Validates if approval is allowed in current state
        /// </summary>
        /// <returns>True if approval is allowed</returns>
        public bool CanApprove()
        {
            return _currentState.CanApprove();
        }

        /// <summary>
        /// Validates if rejection is allowed in current state
        /// </summary>
        /// <returns>True if rejection is allowed</returns>
        public bool CanReject()
        {
            return _currentState.CanReject();
        }

        /// <summary>
        /// Validates if distribution is allowed in current state
        /// </summary>
        /// <returns>True if distribution is allowed</returns>
        public bool CanDistribute()
        {
            return _currentState.CanDistribute();
        }

        /// <summary>
        /// Validates if receiving responses is allowed in current state
        /// </summary>
        /// <returns>True if receiving responses is allowed</returns>
        public bool CanReceiveResponses()
        {
            return _currentState.CanReceiveResponses();
        }

        /// <summary>
        /// Validates if viewing results is allowed in current state
        /// </summary>
        /// <returns>True if viewing results is allowed</returns>
        public bool CanViewResults()
        {
            return _currentState.CanViewResults();
        }

        /// <summary>
        /// Validates if the assessment can be marked as completed
        /// </summary>
        /// <returns>True if assessment can be marked as completed</returns>
        public bool CanMarkAsCompleted()
        {
            return _currentState.CanMarkAsCompleted(_assessment);
        }

        /// <summary>
        /// Gets validation messages for current state
        /// </summary>
        /// <returns>Collection of validation messages</returns>
        public IEnumerable<string> GetValidationMessages()
        {
            return _currentState.GetValidationMessages();
        }

        /// <summary>
        /// Handles state-specific logic
        /// </summary>
        public void Handle()
        {
            _currentState.Handle(_assessment);
        }

        /// <summary>
        /// Transitions to a new state with comprehensive audit logging
        /// </summary>
        /// <param name="targetStatus">Target status to transition to</param>
        /// <param name="action">Action being performed</param>
        /// <param name="reason">Reason for the transition</param>
        /// <param name="localizedActionName">Localized action name for audit trail</param>
        /// <param name="userId">User ID performing the action</param>
        /// <param name="userRole">User role performing the action</param>
        /// <param name="actionDetails">Additional action details</param>
        /// <param name="rejectionReason">Rejection reason (if applicable)</param>
        /// <returns>True if transition was successful, false if not allowed</returns>
        public bool TransitionToWithAudit(AssessmentStatus targetStatus, AssessmentActionEnum action,
            string reason, string localizedActionName, int userId, string userRole, string actionDetails = "", string rejectionReason = "")
        {
            if (!_currentState.CanTransitionTo(targetStatus))
            {
                return false;
            }

            var previousStatus = _assessment.Status;

            // Update the assessment status
            _assessment.Status = targetStatus;

            // Create new state instance
            _currentState = AssessmentStateFactory.CreateState(targetStatus);

            // Add comprehensive audit entry
            AddStatusHistoryEntryWithAudit(targetStatus, action, reason, localizedActionName,
                userId, userRole, actionDetails, previousStatus, targetStatus, rejectionReason);

            return true;
        }

        /// <summary>
        /// Adds an audit entry for non-status-changing actions
        /// </summary>
        /// <param name="action">Action being performed</param>
        /// <param name="reason">Reason for the action</param>
        /// <param name="localizedActionName">Localized action name for audit trail</param>
        /// <param name="userId">User ID performing the action</param>
        /// <param name="userRole">User role performing the action</param>
        /// <param name="actionDetails">Additional action details</param>
        public void AddAuditEntry(AssessmentActionEnum action, string reason, string localizedActionName,
            int userId, string userRole, string actionDetails = "")
        {
            // For non-status-changing actions, set both previousStatus and newStatus to current status
            // This ensures that status information is always available in audit history
            var currentStatus = _assessment.Status;
            AddStatusHistoryEntryWithAudit(currentStatus, action, reason, localizedActionName,
                userId, userRole, actionDetails, currentStatus, currentStatus);
        }

        /// <summary>
        /// Adds a comprehensive status history entry for audit trail with all required fields
        /// Ensures complete audit logging following the notification pattern for localization
        /// </summary>
        /// <param name="status">New status</param>
        /// <param name="action">Action being performed</param>
        /// <param name="reason">Reason for the change</param>
        /// <param name="localizationKey">Localization key reference (NOT translated text) for retrieval localization</param>
        /// <param name="userId">User ID performing the action</param>
        /// <param name="userRole">User role performing the action</param>
        /// <param name="actionDetails">Comprehensive description of the operation performed</param>
        /// <param name="previousStatus">Previous status (for status changes, null for non-status actions)</param>
        /// <param name="newStatus">New status (for status changes, null for non-status actions)</param>
        /// <param name="rejectionReason">Rejection reason (if applicable)</param>
        private void AddStatusHistoryEntryWithAudit(AssessmentStatus status, AssessmentActionEnum action,
            string reason, string localizationKey, int userId, string userRole, string actionDetails,
            AssessmentStatus? previousStatus, AssessmentStatus? newStatus, string rejectionReason = "")
        {
            // Initialize collection if null
            _assessment.StatusHistories ??= new List<AssessmentStatusHistory>();

            // Create comprehensive status history entry with all required audit fields
            var statusHistory = new AssessmentStatusHistory
            {
                // Core assessment reference
                AssessmentId = _assessment.Id,
                AssessmentStatusId = (int)status,

                // Action information
                Action = action,
                Reason = reason,
                RejectionReason = rejectionReason,

                // Comprehensive action details - detailed description of the operation
                ActionDetails = actionDetails,

                // Localization key reference (NOT translated text) following notification pattern
                // This allows proper localization on retrieval for multilingual support
                Notes = localizationKey,

                // User context information
                UserRole = userRole,
                CreatedBy = userId,

                // Status transition information (for status changes)
                PreviousStatus = previousStatus,
                NewStatus = newStatus,

                // Timestamp (automatically set by FullAuditedEntity)
                CreatedAt = DateTime.Now
            };

            _assessment.StatusHistories.Add(statusHistory);
        }

        /// <summary>
        /// Adds a status history entry for audit trail (legacy method for backward compatibility)
        /// </summary>
        /// <param name="status">New status</param>
        /// <param name="action">Action being performed</param>
        /// <param name="reason">Reason for the change</param>
        private void AddStatusHistoryEntry(AssessmentStatus status, AssessmentActionEnum action, string reason)
        {
            // Initialize collection if null
            _assessment.StatusHistories ??= new List<AssessmentStatusHistory>();

            // Add basic status history entry for backward compatibility
            var statusHistory = new AssessmentStatusHistory
            {
                AssessmentId = _assessment.Id,
                AssessmentStatusId = (int)status,
                Reason = reason,
                Action = action
            };

            _assessment.StatusHistories.Add(statusHistory);
        }

        /// <summary>
        /// Initializes state from current assessment status
        /// Should be called after loading from database
        /// </summary>
        public void InitializeState()
        {
            _currentState = AssessmentStateFactory.CreateState(_assessment.Status);
        }
    }
}
