using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for AnswerOption entity
    /// Configures the many-to-many relationship between Answer and Option entities
    /// Provides comprehensive configuration for selected options in choice-based answers
    /// </summary>
    public class AnswerOptionEntityConfig : IEntityTypeConfiguration<AnswerOption>
    {
        public void Configure(EntityTypeBuilder<AnswerOption> builder)
        {
            // Configure primary key
            builder.HasKey(ao => ao.Id);

            // Configure required properties with constraints
            builder.Property(ao => ao.AnswerId)
                .IsRequired()
                .HasComment("Foreign key reference to the Answer this option selection belongs to");

            builder.Property(ao => ao.OptionId)
                .IsRequired()
                .HasComment("Foreign key reference to the Option that was selected");

            // Configure relationships
            builder.HasOne(ao => ao.Answer)
                .WithMany(a => a.SelectedOptions)
                .HasForeignKey(ao => ao.AnswerId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AnswerOptions_Answers_AnswerId");

            builder.HasOne(ao => ao.Option)
                .WithMany()
                .HasForeignKey(ao => ao.OptionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AnswerOptions_Options_OptionId");

            // Configure unique constraint to prevent duplicate selections
            builder.HasIndex(ao => new { ao.AnswerId, ao.OptionId })
                .IsUnique()
                .HasDatabaseName("IX_AnswerOptions_AnswerId_OptionId_Unique")
                .HasFilter("[IsDeleted] = 0");

            // Configure indexes for performance optimization
            builder.HasIndex(ao => ao.AnswerId)
                .HasDatabaseName("IX_AnswerOptions_AnswerId_Performance");

            builder.HasIndex(ao => ao.OptionId)
                .HasDatabaseName("IX_AnswerOptions_OptionId_Performance");

            // Configure table name
            builder.ToTable("AnswerOptions");

            // Configure audit properties (inherited from FullAuditedEntity)
            builder.Property(ao => ao.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the answer option was created");

            builder.Property(ao => ao.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the answer option was last updated");

            builder.Property(ao => ao.IsDeleted)
                .HasDefaultValue(false)
                .HasComment("Soft delete flag");

          
        }
    }
}
