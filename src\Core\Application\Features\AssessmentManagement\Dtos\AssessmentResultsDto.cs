using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Results and Analytics
    /// Based on User Story 5: View Compiled Assessment Results
    /// Contains comprehensive assessment results, statistics, and analytics
    /// </summary>
    public record AssessmentResultsDto : BaseDto
    {
        /// <summary>
        /// Assessment basic information
        /// Arabic: المعلومات الأساسية للتقييم
        /// </summary>
        public AssessmentBasicInfoDto Assessment { get; set; } = new();

        /// <summary>
        /// Overall assessment statistics
        /// Arabic: الإحصائيات العامة للتقييم
        /// </summary>
        public AssessmentOverallStatisticsDto OverallStatistics { get; set; } = new();

        /// <summary>
        /// Question-by-question analysis (for questionnaire type)
        /// Arabic: تحليل سؤال بسؤال
        /// </summary>
        public List<QuestionAnalysisDto>? QuestionAnalysis { get; set; }

        /// <summary>
        /// Individual response summaries
        /// Arabic: ملخصات الردود الفردية
        /// </summary>
        public List<ResponseSummaryDto>? ResponseSummaries { get; set; }

        /// <summary>
        /// Response timeline and trends
        /// Arabic: الجدول الزمني واتجاهات الردود
        /// </summary>
        public ResponseTimelineDto? ResponseTimeline { get; set; }

        /// <summary>
        /// Completion analytics
        /// Arabic: تحليلات الإكمال
        /// </summary>
        public CompletionAnalyticsDto? CompletionAnalytics { get; set; }

        /// <summary>
        /// Export options available
        /// Arabic: خيارات التصدير المتاحة
        /// </summary>
        public List<string> AvailableExportFormats { get; set; } = new() { "PDF", "Excel", "CSV" };

        /// <summary>
        /// Results generation date
        /// Arabic: تاريخ إنشاء النتائج
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// Whether results are final (assessment completed)
        /// Arabic: النتائج نهائية
        /// </summary>
        public bool IsFinal { get; set; }
    }





}
