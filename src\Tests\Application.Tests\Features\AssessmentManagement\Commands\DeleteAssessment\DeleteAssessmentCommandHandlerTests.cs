using Xunit;
using Moq;
using Microsoft.Extensions.Localization;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Commands.DeleteAssessment;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Resources;
using System.Net;

namespace Application.Tests.Features.AssessmentManagement.Commands.DeleteAssessment
{
    /// <summary>
    /// Unit tests for DeleteAssessmentCommandHandler
    /// Tests the CQRS command handler for deleting assessments
    /// </summary>
    public class DeleteAssessmentCommandHandlerTests
    {
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly DeleteAssessmentCommandHandler _handler;

        public DeleteAssessmentCommandHandlerTests()
        {
            _mockLogger = new Mock<ILoggerManager>();
            _mockRepository = new Mock<IRepositoryManager>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();

            _handler = new DeleteAssessmentCommandHandler(
                _mockLogger.Object,
                _mockRepository.Object,
                _mockLocalizer.Object,
                _mockCurrentUserService.Object);
        }

        [Fact]
        public async Task Handle_ValidDraftAssessment_ReturnsSuccess()
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var command = new DeleteAssessmentCommand { Id = assessmentId };

            var mockFund = new Fund
            {
                Id = 1,
                Name = "Test Fund",
                FundManagers = new List<FundManager>
                {
                    new FundManager { UserId = userId, FundId = 1 }
                }
            };

            var mockAssessment = new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Status = AssessmentStatus.Draft,
                FundId = 1,
                Questions = new List<AssessmentQuestion>()
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockCurrentUserService.Setup(x => x.UserName).Returns("Test User");
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, true))
                .ReturnsAsync(mockAssessment);
            _mockRepository.Setup(x => x.Fund.GetByIdAsync<Fund>(1, false))
                .ReturnsAsync(mockFund);
            _mockRepository.Setup(x => x.Assessment.UpdateAsync(It.IsAny<Assessment>()))
                .ReturnsAsync(true);
            _mockLocalizer.Setup(x => x["ItemDeletedSuccessfully"]).Returns(new LocalizedString("ItemDeletedSuccessfully", "Item deleted successfully"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Succeeded);
            Assert.Equal("Item deleted successfully", result.Message);
            
            // Verify that the assessment was marked as deleted
            Assert.True(mockAssessment.IsDeleted);
            Assert.NotNull(mockAssessment.DeletedAt);
            Assert.Equal(userId, mockAssessment.DeletedBy);
        }

        [Fact]
        public async Task Handle_NonDraftAssessment_ReturnsBadRequest()
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var command = new DeleteAssessmentCommand { Id = assessmentId };

            var mockFund = new Fund
            {
                Id = 1,
                Name = "Test Fund",
                FundManagers = new List<FundManager>
                {
                    new FundManager { UserId = userId, FundId = 1 }
                }
            };

            var mockAssessment = new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Status = AssessmentStatus.Active, // Not draft
                FundId = 1
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, true))
                .ReturnsAsync(mockAssessment);
            _mockRepository.Setup(x => x.Fund.GetByIdAsync<Fund>(1, false))
                .ReturnsAsync(mockFund);
            _mockLocalizer.Setup(x => x["AssessmentCannotBeDeleted"]).Returns(new LocalizedString("AssessmentCannotBeDeleted", "Assessment cannot be deleted"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Succeeded);
            Assert.Equal(HttpStatusCode.BadRequest, result.StatusCode);
        }

        [Fact]
        public async Task Handle_AssessmentNotFound_ReturnsNotFound()
        {
            // Arrange
            var assessmentId = 999;
            var command = new DeleteAssessmentCommand { Id = assessmentId };

            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, true))
                .ReturnsAsync((Assessment?)null);
            _mockLocalizer.Setup(x => x["AssessmentNotFound"]).Returns(new LocalizedString("AssessmentNotFound", "Assessment not found"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Succeeded);
            Assert.Equal(HttpStatusCode.NotFound, result.StatusCode);
        }

        [Fact]
        public async Task Handle_UnauthorizedUser_ReturnsUnauthorized()
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var unauthorizedUserId = 456;
            var command = new DeleteAssessmentCommand { Id = assessmentId };

            var mockFund = new Fund
            {
                Id = 1,
                Name = "Test Fund",
                FundManagers = new List<FundManager>
                {
                    new FundManager { UserId = userId, FundId = 1 } // Different user
                }
            };

            var mockAssessment = new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Status = AssessmentStatus.Draft,
                FundId = 1
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(unauthorizedUserId);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, true))
                .ReturnsAsync(mockAssessment);
            _mockRepository.Setup(x => x.Fund.GetByIdAsync<Fund>(1, false))
                .ReturnsAsync(mockFund);
            _mockLocalizer.Setup(x => x["UnauthorizedAccess"]).Returns(new LocalizedString("UnauthorizedAccess", "Unauthorized access"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Succeeded);
            Assert.Equal(HttpStatusCode.Unauthorized, result.StatusCode);
        }
    }
}
