using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Mapping.AssessmentManagement
{
    /// <summary>
    /// Mapping configurations for retrieving Assessment entities
    /// Maps from domain entities to DTOs for read operations
    /// Follows the same pattern as Resolution GetResolutionMapping for consistency
    /// </summary>
    public partial class AssessmentProfile
    {
        public void GetAssessmentMapping()
        {
            // Assessment entity to AssessmentDto
            CreateMap<Assessment, AssessmentDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.StatusDisplayName, opt => opt.Ignore()) // Will be set by localization
                .ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Will be resolved separately
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt));

            // Assessment entity to AssessmentListDto
            CreateMap<Assessment, AssessmentListDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.TypeDisplayName, opt => opt.Ignore()) // Will be set by localization
                .ForMember(dest => dest.StatusDisplayName, opt => opt.Ignore()) // Will be set by localization
                .ForMember(dest => dest.QuestionCount, opt => opt.MapFrom(src => src.Questions != null ? src.Questions.Count : 0))
                .ForMember(dest => dest.AttachmentCount, opt => opt.MapFrom(src => src.Attachment != null ? 1 : 0))
                .ForMember(dest => dest.TotalExpectedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count : 0))
                .ForMember(dest => dest.CompletedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed) : 0))
                .ForMember(dest => dest.PendingResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Pending) : 0))
                .ForMember(dest => dest.CompletionPercentage, opt => opt.MapFrom(src => CalculateCompletionPercentage(src)))
                .ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Will be resolved separately
                .ForMember(dest => dest.DaysRemaining, opt => opt.MapFrom(src => CalculateDaysRemaining(src.DueDate)))
                .ForMember(dest => dest.IsOverdue, opt => opt.MapFrom(src => src.DueDate.HasValue && src.DueDate.Value < DateTime.Now))
                .ForMember(dest => dest.Priority, opt => opt.MapFrom(src => CalculatePriority(src)))
                // Action permissions will be set in the handler based on user role and status
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CanApprove, opt => opt.Ignore())
                .ForMember(dest => dest.CanReject, opt => opt.Ignore())
                .ForMember(dest => dest.CanDistribute, opt => opt.Ignore())
                .ForMember(dest => dest.CanViewResults, opt => opt.Ignore())
                .ForMember(dest => dest.CanRespond, opt => opt.Ignore());

            // Assessment entity to SingleAssessmentResponse
            CreateMap<Assessment, SingleAssessmentResponse>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.AssessmentType, opt => opt.MapFrom(src => MapAssessmentType(src.Type)))
                .ForMember(dest => dest.AssessmentStatus, opt => opt.MapFrom(src => MapAssessmentStatus(src.Status)))
                .ForMember(dest => dest.LastUpdated, opt => opt.MapFrom(src => src.UpdatedAt ?? src.CreatedAt))
                .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => (int)src.Status))
                .ForMember(dest => dest.CreatorID, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.QuestionCount, opt => opt.MapFrom(src => src.Questions != null ? src.Questions.Count : 0))
                .ForMember(dest => dest.AttachmentCount, opt => opt.MapFrom(src => src.Attachment != null ? 1 : 0))
                .ForMember(dest => dest.TotalExpectedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count : 0))
                .ForMember(dest => dest.CompletedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed) : 0))
                .ForMember(dest => dest.CompletionPercentage, opt => opt.MapFrom(src => CalculateCompletionPercentage(src)))
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedByUser != null ? src.CreatedByUser.FullName : string.Empty))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                //.ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Will be resolved separately
                // Action permissions will be set in the handler based on user role and status
                .ForMember(dest => dest.CanApprove, opt => opt.Ignore())
                .ForMember(dest => dest.CanReject, opt => opt.Ignore())
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CanDistribute, opt => opt.Ignore())
                .ForMember(dest => dest.CanView, opt => opt.Ignore())
                .ForMember(dest => dest.CanViewResults, opt => opt.Ignore())
                .ForMember(dest => dest.CanRespond, opt => opt.Ignore());

            // Assessment entity to AssessmentDetailsDto
            CreateMap<Assessment, AssessmentDetailsDto>()
                .IncludeBase<Assessment, AssessmentDto>()
                .ForMember(dest => dest.StatusHistory, opt => opt.Ignore()) // Will be mapped separately
                .ForMember(dest => dest.Responses, opt => opt.Ignore()) // Will be mapped separately
                .ForMember(dest => dest.AssignedBoardMembers, opt => opt.Ignore()) // Will be mapped separately
                .ForMember(dest => dest.Statistics, opt => opt.Ignore()) // Will be calculated separately
                .ForMember(dest => dest.AllowedTransitions, opt => opt.Ignore()) // Will be set by state machine
                .ForMember(dest => dest.ValidationMessages, opt => opt.Ignore()) // Will be set by validation
                .ForMember(dest => dest.CanRespond, opt => opt.Ignore()) // Will be set by business logic
                .ForMember(dest => dest.RejectionReason, opt => opt.Ignore()) // Will be set by business logic
                .ForMember(dest => dest.DistributionDate, opt => opt.Ignore()) // Will be set by business logic
                .ForMember(dest => dest.CompletionDate, opt => opt.Ignore()) // Will be set by business logic
                .ForMember(dest => dest.LastResponseDate, opt => opt.Ignore()) // Will be calculated separately
                .ForMember(dest => dest.AverageResponseTime, opt => opt.Ignore()); // Will be calculated separately

            // Assessment entity to AssessmentByIdDto (for GET /api/assessments/{id} endpoint)
            CreateMap<Assessment, AssessmentByIdDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.Questions, opt => opt.MapFrom(src => src.Type == Domain.Entities.AssessmentManagement.Enums.AssessmentType.Questionnaire ? src.Questions.OrderBy(q => q.DisplayOrder) : null))
                .ForMember(dest => dest.Attachment, opt => opt.MapFrom(src => src.Type == Domain.Entities.AssessmentManagement.Enums.AssessmentType.Attachment ? src.Attachment : null));

            // AssessmentQuestion entity to AssessmentQuestionByIdDto
            CreateMap<AssessmentQuestion, AssessmentQuestionByIdDto>()
                .ForMember(dest => dest.Options, opt => opt.MapFrom(src => src.Options.OrderBy(o => o.Order)));

            // Option entity to AssessmentOptionByIdDto
            CreateMap<Option, AssessmentOptionByIdDto>();

            // Attachment entity to AssessmentAttachmentByIdDto
            CreateMap<Domain.Entities.Shared.Attachment, AssessmentAttachmentByIdDto>();

            // Comprehensive Assessment Mappings
            CreateMap<Assessment, ComprehensiveAssessmentDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.ReviewerName, opt => opt.MapFrom(src => src.Reviewer != null ? src.Reviewer.UserName : string.Empty))
                .ForMember(dest => dest.Questions, opt => opt.MapFrom(src => src.Questions.OrderBy(q => q.DisplayOrder)))
                .ForMember(dest => dest.Responses, opt => opt.MapFrom(src => src.Responses))
                .ForMember(dest => dest.Statistics, opt => opt.Ignore()); // Calculated in handler

            CreateMap<AssessmentQuestion, ComprehensiveQuestionDto>()
                .ForMember(dest => dest.Options, opt => opt.MapFrom(src => src.Options.OrderBy(o => o.Order)));

            CreateMap<Option, ComprehensiveOptionDto>();

            CreateMap<AssessmentResponse, ComprehensiveResponseDto>()
                .ForMember(dest => dest.RespondentName, opt => opt.Ignore()) // Will be resolved separately
                .ForMember(dest => dest.Answers, opt => opt.MapFrom(src => src.Answers));

            CreateMap<Answer, ComprehensiveAnswerDto>()
                .ForMember(dest => dest.QuestionText, opt => opt.MapFrom(src => src.Question != null ? src.Question.QuestionText : string.Empty))
                .ForMember(dest => dest.SelectedOptions, opt => opt.MapFrom(src => src.SelectedOptions));

            CreateMap<AnswerOption, ComprehensiveAnswerOptionDto>()
                .ForMember(dest => dest.OptionText, opt => opt.MapFrom(src => src.Option != null ? src.Option.Value : string.Empty))
                .ForMember(dest => dest.IsCorrect, opt => opt.MapFrom(src => src.Option != null ? src.Option.IsCorrect : false));

            CreateMap<Domain.Entities.Shared.Attachment, ComprehensiveAttachmentDto>()
                .ForMember(dest => dest.UploadedAt, opt => opt.MapFrom(src => src.CreatedAt));
        }

        #region Helper Methods

        /// <summary>
        /// Maps AssessmentType enum to AssessmentTypeDto
        /// </summary>
        private static AssessmentTypeDto MapAssessmentType(AssessmentType type)
        {
            return new AssessmentTypeDto
            {
                Id = (int)type,
                Value = type,
                Description = type.ToString(),
                NameAr = GetAssessmentTypeNameAr(type),
                NameEn = GetAssessmentTypeNameEn(type)
            };
        }

        /// <summary>
        /// Maps AssessmentStatus enum to AssessmentStatusDto
        /// </summary>
        private static AssessmentStatusDto MapAssessmentStatus(AssessmentStatus status)
        {
            return new AssessmentStatusDto
            {
                Id = (int)status,
                Value = status,
                Description = status.ToString(),
                NameAr = GetAssessmentStatusNameAr(status),
                NameEn = GetAssessmentStatusNameEn(status)
            };
        }

        /// <summary>
        /// Calculates completion percentage for an assessment
        /// </summary>
        private static decimal CalculateCompletionPercentage(Assessment assessment)
        {
            if (assessment.Responses == null || !assessment.Responses.Any())
                return 0;

            var totalResponses = assessment.Responses.Count;
            var completedResponses = assessment.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed);

            return totalResponses > 0 ? Math.Round((decimal)completedResponses / totalResponses * 100, 2) : 0;
        }

        /// <summary>
        /// Calculates days remaining until due date
        /// </summary>
        private static int? CalculateDaysRemaining(DateTime? dueDate)
        {
            if (!dueDate.HasValue)
                return null;

            var days = (dueDate.Value.Date - DateTime.Now.Date).Days;
            return days >= 0 ? days : 0;
        }

        /// <summary>
        /// Calculates priority based on due date and status
        /// </summary>
        private static string CalculatePriority(Assessment assessment)
        {
            if (!assessment.DueDate.HasValue)
                return "Medium";

            var daysRemaining = CalculateDaysRemaining(assessment.DueDate);
            
            if (daysRemaining.HasValue)
            {
                if (daysRemaining.Value <= 1)
                    return "High";
                else if (daysRemaining.Value <= 7)
                    return "Medium";
                else
                    return "Low";
            }

            return "Medium";
        }

        /// <summary>
        /// Gets Arabic name for assessment type
        /// </summary>
        private static string GetAssessmentTypeNameAr(AssessmentType type)
        {
            return type switch
            {
                AssessmentType.Questionnaire => "استبيان",
                AssessmentType.Attachment => "مرفق",
                _ => type.ToString()
            };
        }

        /// <summary>
        /// Gets English name for assessment type
        /// </summary>
        private static string GetAssessmentTypeNameEn(AssessmentType type)
        {
            return type switch
            {
                AssessmentType.Questionnaire => "Questionnaire",
                AssessmentType.Attachment => "Attachment",
                _ => type.ToString()
            };
        }

        /// <summary>
        /// Gets Arabic name for assessment status
        /// </summary>
        private static string GetAssessmentStatusNameAr(AssessmentStatus status)
        {
            return status switch
            {
                AssessmentStatus.Draft => "مسودة",
                AssessmentStatus.WaitingForApproval => "في انتظار الموافقة",
                AssessmentStatus.Approved => "موافق عليه",
                AssessmentStatus.Rejected => "مرفوض",
                AssessmentStatus.Active => "نشط",
                AssessmentStatus.Completed => "مكتمل",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Gets English name for assessment status
        /// </summary>
        private static string GetAssessmentStatusNameEn(AssessmentStatus status)
        {
            return status switch
            {
                AssessmentStatus.Draft => "Draft",
                AssessmentStatus.WaitingForApproval => "Waiting for Approval",
                AssessmentStatus.Approved => "Approved",
                AssessmentStatus.Rejected => "Rejected",
                AssessmentStatus.Active => "Active",
                AssessmentStatus.Completed => "Completed",
                _ => status.ToString()
            };
        }

        #endregion
    }
}
