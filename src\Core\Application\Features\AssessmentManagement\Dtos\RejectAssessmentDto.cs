namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for rejecting an assessment
    /// Based on User Story 2: Approve or Reject Assessment
    /// </summary>
    public record RejectAssessmentDto
    {
        /// <summary>
        /// Assessment ID to reject
        /// Arabic: معرف التقييم للرفض
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Reason for rejection (required)
        /// Arabic: سبب الرفض
        /// </summary>
        public string RejectionReason { get; set; } = string.Empty;

        /// <summary>
        /// Additional comments (optional)
        /// Arabic: تعليقات إضافية
        /// </summary>
        public string? Comments { get; set; }
    }
}
