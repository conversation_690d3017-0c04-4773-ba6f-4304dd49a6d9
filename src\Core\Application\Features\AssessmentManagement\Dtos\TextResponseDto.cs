namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Text Response
    /// Arabic: كائل نقل البيانات للرد النصي
    /// </summary>
    public record TextResponseDto
    {
        /// <summary>
        /// Response ID
        /// Arabic: معرف الرد
        /// </summary>
        public int ResponseId { get; set; }

        /// <summary>
        /// Respondent name (if not anonymous)
        /// Arabic: اسم المجيب
        /// </summary>
        public string? RespondentName { get; set; }

        /// <summary>
        /// Text answer
        /// Arabic: الإجابة النصية
        /// </summary>
        public string TextAnswer { get; set; } = string.Empty;

        /// <summary>
        /// Response date
        /// Arabic: تاريخ الرد
        /// </summary>
        public DateTime ResponseDate { get; set; }

        /// <summary>
        /// Answer length in characters
        /// Arabic: طول الإجابة بالأحرف
        /// </summary>
        public int AnswerLength { get; set; }
    }
}
