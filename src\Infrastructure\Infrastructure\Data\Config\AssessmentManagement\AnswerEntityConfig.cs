using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for Answer entity
    /// Configures navigation properties, relationships, and constraints for assessment answers
    /// Provides comprehensive configuration for individual question answers within responses
    /// </summary>
    public class AnswerEntityConfig : IEntityTypeConfiguration<Answer>
    {
        public void Configure(EntityTypeBuilder<Answer> builder)
        {
            // Configure primary key
            builder.HasKey(a => a.Id);

            // Configure required properties with constraints
            builder.Property(a => a.ResponseId)
                .IsRequired()
                .HasComment("Foreign key reference to the AssessmentResponse this answer belongs to");

            builder.Property(a => a.QuestionId)
                .IsRequired()
                .HasComment("Foreign key reference to the AssessmentQuestion this answer is for");

            // Configure optional properties
            builder.Property(a => a.AnswerValue)
                .HasMaxLength(4000)
                .HasComment("The actual answer value provided by the board member (for text questions)");

            // Configure relationships
            builder.HasOne(a => a.Response)
                .WithMany(r => r.Answers)
                .HasForeignKey(a => a.ResponseId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Answers_AssessmentResponses_ResponseId");

            builder.HasOne(a => a.Question)
                .WithMany(q => q.Answers)
                .HasForeignKey(a => a.QuestionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Answers_AssessmentQuestions_QuestionId");

            // Configure one-to-many relationships for selected options (for choice questions)
            builder.HasMany(a => a.SelectedOptions)
                .WithOne(ao => ao.Answer)
                .HasForeignKey(ao => ao.AnswerId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AnswerOptions_Answers_AnswerId");

            // Configure unique constraint to ensure one answer per question per response
            builder.HasIndex(a => new { a.ResponseId, a.QuestionId })
                .IsUnique()
                .HasDatabaseName("IX_Answers_ResponseId_QuestionId_Unique")
                .HasFilter("[IsDeleted] = 0");

            // Configure indexes for performance optimization
            builder.HasIndex(a => a.ResponseId)
                .HasDatabaseName("IX_Answers_ResponseId_Performance");

            builder.HasIndex(a => a.QuestionId)
                .HasDatabaseName("IX_Answers_QuestionId_Performance");

            // Configure table name
            builder.ToTable("Answers");

            // Configure audit properties (inherited from FullAuditedEntity)
            builder.Property(a => a.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the answer was created");

            builder.Property(a => a.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the answer was last updated");

            builder.Property(a => a.IsDeleted)
                .HasDefaultValue(false)
                .HasComment("Soft delete flag");

           
        }
    }
}
