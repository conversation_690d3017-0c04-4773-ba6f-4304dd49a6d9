using Abstraction.Base.Dto;
using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.AssessmentManagement.Commands.DeleteAssessment
{
    /// <summary>
    /// Command for deleting an existing assessment
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as DeleteResolutionCommand for consistency
    /// Arabic: أمر حذف التقييم الموجود
    /// </summary>
    public record DeleteAssessmentCommand : BaseDto, ICommand<BaseResponse<string>>
    {
        // Command inherits Id property from BaseDto
        // No additional properties needed for delete operation
    }
}
