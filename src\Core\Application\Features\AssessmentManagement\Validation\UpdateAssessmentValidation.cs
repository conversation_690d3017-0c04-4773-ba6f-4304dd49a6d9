using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Commands.UpdateAssessment;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for UpdateAssessmentCommand
    /// Implements comprehensive validation for assessment updates (draft only)
    /// Follows the same pattern as EditResolutionValidation for consistency
    /// </summary>
    public class UpdateAssessmentValidation : BaseAssessmentValidation<UpdateAssessmentCommand>
    {
        public UpdateAssessmentValidation(IStringLocalizer<SharedResources> localizer) : base(localizer)
        {
            //// Validate assessment ID
            //ValidateAssessmentId(x => x.Id);

            //// Validate basic assessment properties
            //ValidateAssessmentTitle(x => x.Title);
            //ValidateAssessmentDescription(x => x.Description);
            //ValidateAssessmentInstructions(x => x.Instructions);
            //ValidateAssessmentType(x => x.Type);
            //ValidateFundId(x => x.FundId);
            //ValidateDueDate(x => x.DueDate);

            //// Validate questions based on assessment type
            //ValidateAssessmentQuestions(x => x.Questions, x => x.Type);

            //// Validate attachments based on assessment type
            //ValidateAssessmentAttachments(x => x.Attachments, x => x.Type);
        }
    }
}
