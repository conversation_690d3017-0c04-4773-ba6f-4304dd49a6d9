namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Response Timeline
    /// Arabic: كائل نقل البيانات للجدول الزمني للردود
    /// </summary>
    public record ResponseTimelineDto
    {
        /// <summary>
        /// Daily response counts
        /// Arabic: عدد الردود اليومية
        /// </summary>
        public List<DailyResponseCountDto> DailyResponseCounts { get; set; } = new();

        /// <summary>
        /// Response velocity (responses per day)
        /// Arabic: سرعة الاستجابة (ردود في اليوم)
        /// </summary>
        public decimal ResponseVelocity { get; set; }

        /// <summary>
        /// Peak response day
        /// Arabic: يوم ذروة الردود
        /// </summary>
        public DateTime? PeakResponseDay { get; set; }

        /// <summary>
        /// Peak response count
        /// Arabic: عدد ردود الذروة
        /// </summary>
        public int PeakResponseCount { get; set; }

        /// <summary>
        /// Projected completion date (if not completed)
        /// Arabic: تاريخ الإكمال المتوقع
        /// </summary>
        public DateTime? ProjectedCompletionDate { get; set; }
    }
}
