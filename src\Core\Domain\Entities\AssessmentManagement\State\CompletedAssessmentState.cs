using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Represents a completed assessment where all responses have been collected
    /// Terminal state - no further transitions allowed
    /// Allows viewing final results but no modifications
    /// Based on User Story 5: View Compiled Assessment Results
    /// </summary>
    public class CompletedAssessmentState : IAssessmentState
    {
        public AssessmentStatus Status => AssessmentStatus.Completed;

        public void Handle(Assessment assessment)
        {
            // Completed state - assessment is finalized with all responses collected
            // No specific handling required as this is a terminal state
        }

        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return false; // Terminal state - no transitions allowed
        }

        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return Enumerable.Empty<AssessmentStatus>(); // No transitions allowed
        }

        public bool CanEdit()
        {
            return false; // Completed assessments cannot be edited
        }

        public bool CanDelete()
        {
            return false; // Completed assessments cannot be deleted
        }

        public bool CanSubmitForApproval()
        {
            return false; // Already completed
        }

        public bool CanApprove()
        {
            return false; // Already completed
        }

        public bool CanReject()
        {
            return false; // Cannot reject a completed assessment
        }

        public bool CanDistribute()
        {
            return false; // Already distributed and completed
        }

        public bool CanReceiveResponses()
        {
            return false; // No longer accepting responses
        }

        public bool CanViewResults()
        {
            return true; // Completed assessments can show final results
        }

        public bool CanMarkAsCompleted(Assessment assessment)
        {
            return false; // Already completed
        }

        public IEnumerable<string> GetValidationMessages()
        {
            return new[]
            {
                "Assessment has been completed with all responses collected",
                "Assessment is read-only and cannot be modified",
                "Final results and analytics are available for viewing"
            };
        }

        public string GetStateDescriptionKey()
        {
            return "AssessmentInCompletedState";
        }

        public string GetStateDisplayNameKey()
        {
            return "AssessmentStatusCompleted";
        }
    }
}
