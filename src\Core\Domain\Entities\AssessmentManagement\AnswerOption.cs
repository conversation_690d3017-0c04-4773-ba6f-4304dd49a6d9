using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.AssessmentManagement
{
    /// <summary>
    /// Represents an individual answer to a specific question within an assessment response
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Properties are defined based on requirements in AssessmentStories.md
    /// </summary>
    public class AnswerOption : FullAuditedEntity
    {
        /// <summary>
        /// Foreign key reference to the AssessmentQuestionOption 
        /// Required field as specified in user stories
        /// </summary>
        public int OptionId { get; set; }

        /// <summary>
        /// Foreign key reference to the AssessmentAnswer
        /// Required field as specified in user stories
        /// </summary>
        public int AnswerId{ get; set; }

        /// <summary>
        /// Navigation property to the AssessmentQuestionOption this answer is for
        /// Provides access to question information
        /// </summary>
        [ForeignKey("OptionId")]
        public virtual Option Option { get; set; } = null!;
        
        /// <summary>
        /// Navigation property to the AssessmentAnswer is for
        /// Provides access to question information
        /// </summary>
        [ForeignKey("AnswerId")]
        public virtual Answer Answer { get; set; } = null!;
    } 
}
