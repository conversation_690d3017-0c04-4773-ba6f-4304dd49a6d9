using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Commands.CreateAssessment
{
    /// <summary>
    /// Command for creating a new assessment
    /// Based on User Story 1: Create New Assessment
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as AddResolutionCommand for consistency
    /// </summary>
    public record CreateAssessmentCommand : CreateAssessmentDto, ICommand<BaseResponse<string>>
    {
        // Command inherits all properties from CreateAssessmentDto
        // No additional properties needed unless specific to command execution
    }
}
