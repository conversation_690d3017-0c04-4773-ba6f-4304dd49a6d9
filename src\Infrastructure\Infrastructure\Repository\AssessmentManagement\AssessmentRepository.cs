using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.AssessmentManagement;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Abstraction.Constants;

namespace Infrastructure.Repository.AssessmentManagement
{
    /// <summary>
    /// Repository implementation for Assessment entity operations
    /// Provides data access functionality using Entity Framework Core
    /// </summary>
    public class AssessmentRepository : GenericRepository, IAssessmentRepository
    {
        public AssessmentRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }

        /// <summary>
        /// Gets assessments by role-based filtering
        /// Follows the same pattern as Resolution GetResolutionsByRoleAsync
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="searchTerm">Search term for filtering</param>
        /// <param name="type">Assessment type filter</param>
        /// <param name="status">Assessment status filter</param>
        /// <param name="fromDate">Start date filter</param>
        /// <param name="toDate">End date filter</param>
        /// <param name="userRole">User role for filtering</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of assessments filtered by role</returns>
        public IQueryable<Assessment> GetAssessmentsByRoleAsync(int fundId, string? searchTerm, AssessmentType? type,
            AssessmentStatus? status, DateTime? fromDate, DateTime? toDate, string userRole, bool trackChanges = false)
        {
            var query = GetByCondition<Assessment>(
                a => a.FundId == fundId &&
                     (a.IsDeleted == false || a.IsDeleted == null) &&
                     (string.IsNullOrEmpty(searchTerm) || a.Title.Contains(searchTerm) || (a.Description != null && a.Description.Contains(searchTerm))) &&
                     (!type.HasValue || a.Type == type.Value) &&
                     (!status.HasValue || a.Status == status.Value) &&
                     (!fromDate.HasValue || a.CreatedAt >= fromDate.Value) &&
                     (!toDate.HasValue || a.CreatedAt <= toDate.Value),
                trackChanges);

            // Apply role-based filtering
            switch (userRole.ToLower())
            {
                case RoleHelper.FundManager:
                    // Fund managers can see all assessments
                    break;
                case RoleHelper.LegalCouncil:
                case RoleHelper.BoardSecretary:
                    // Legal council and board secretary can see non-draft assessments
                    query = query.Where(a => a.Status != AssessmentStatus.Draft);
                    break;
                case RoleHelper.BoardMember:
                    // Board members can only see active and completed assessments
                    query = query.Where(a => a.Status == AssessmentStatus.Active ||
                                           a.Status == AssessmentStatus.Completed);
                    break;
                default:
                    // Unknown role, return empty collection
                    return Enumerable.Empty<Assessment>().AsQueryable();
            }

            return query
                .Include(a => a.StatusHistories.OrderByDescending(x => x.CreatedAt).Take(1));
        }

        /// <summary>
        /// Gets assessment with all related data (questions, responses, attachments, history)
        /// Follows the same pattern as Resolution GetResolutionWithAllDataAsync
        /// </summary>
        /// <param name="assessmentId">Assessment identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Assessment with all related data or null</returns>
        public async Task<Assessment?> GetAssessmentWithDetailsAsync(int assessmentId, bool trackChanges = false)
        {
            return await GetByCondition<Assessment>(
                a => a.Id == assessmentId && (a.IsDeleted == false || a.IsDeleted == null),
                trackChanges)
                .Include(a => a.Fund)
                .Include(a => a.Attachment)
                .Include(a => a.Reviewer)
                .Include(a => a.Questions.OrderBy(q => q.DisplayOrder))
                .ThenInclude(q => q.Options.OrderBy(o => o.Order))
                .Include(a => a.Responses)
                .Include(a => a.StatusHistories.OrderByDescending(x => x.CreatedAt))
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Gets assessment with comprehensive details including responses, answers, and answer options
        /// Used for detailed assessment analysis and review
        /// </summary>
        /// <param name="assessmentId">Assessment identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Assessment with comprehensive data or null</returns>
        public async Task<Assessment?> GetAssessmentWithComprehensiveDetailsAsync(int assessmentId, bool trackChanges = false)
        {
            return await GetByCondition<Assessment>(
                a => a.Id == assessmentId && (a.IsDeleted == false || a.IsDeleted == null),
                trackChanges)
                .Include(a => a.Fund)
                .Include(a => a.Attachment)
                .Include(a => a.Reviewer)
                .Include(a => a.Questions.OrderBy(q => q.DisplayOrder))
                .ThenInclude(q => q.Options.OrderBy(o => o.Order))
                .Include(a => a.Responses)
                .ThenInclude(r => r.Answers)
                .ThenInclude(ans => ans.SelectedOptions)
                .ThenInclude(so => so.Option)
                .Include(a => a.Responses)
                .ThenInclude(r => r.Answers)
                .ThenInclude(ans => ans.Question)
                .Include(a => a.StatusHistories.OrderByDescending(x => x.CreatedAt))
                .FirstOrDefaultAsync();
        }
    }
}
