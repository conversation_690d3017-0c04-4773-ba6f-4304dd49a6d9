using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Represents an approved assessment ready for distribution
    /// Can transition to Active state when distributed
    /// Does not allow editing, deletion, or further approval/rejection
    /// Based on User Story 3: Distribute Assessment
    /// </summary>
    public class ApprovedAssessmentState : IAssessmentState
    {
        public AssessmentStatus Status => AssessmentStatus.Approved;

        public void Handle(Assessment assessment)
        {
            // Approved state - assessment is ready for distribution
            // No specific handling required
        }

        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return targetStatus == AssessmentStatus.Active;
        }

        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return new[]
            {
                AssessmentStatus.Active
            };
        }

        public bool CanEdit()
        {
            return false; // Approved assessments cannot be edited
        }

        public bool CanDelete()
        {
            return false; // Approved assessments cannot be deleted
        }

        public bool CanSubmitForApproval()
        {
            return false; // Already approved
        }

        public bool CanApprove()
        {
            return false; // Already approved
        }

        public bool CanReject()
        {
            return false; // Cannot reject an approved assessment
        }

        public bool CanDistribute()
        {
            return true; // Approved assessments can be distributed
        }

        public bool CanReceiveResponses()
        {
            return false; // Must be active to receive responses
        }

        public bool CanViewResults()
        {
            return false; // No results available until assessment is active
        }

        public bool CanMarkAsCompleted(Assessment assessment)
        {
            return false; // Cannot be completed until distributed and responses collected
        }

        public IEnumerable<string> GetValidationMessages()
        {
            return new[]
            {
                "Assessment has been approved and is ready for distribution",
                "Assessment can be distributed to board members by Fund Manager"
            };
        }

        public string GetStateDescriptionKey()
        {
            return "AssessmentInApprovedState";
        }

        public string GetStateDisplayNameKey()
        {
            return "AssessmentStatusApproved";
        }
    }
}
