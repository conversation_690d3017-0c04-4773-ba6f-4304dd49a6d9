using Abstraction.Base.Dto;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Response Attachment
    /// Arabic: كائل نقل البيانات لمرفق رد التقييم
    /// </summary>
    public record AssessmentResponseAttachmentDto : BaseDto
    {
        /// <summary>
        /// Attachment file ID
        /// Arabic: معرف الملف المرفق
        /// </summary>
        public int AttachmentId { get; set; }

        /// <summary>
        /// Attachment file name
        /// Arabic: اسم الملف المرفق
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// Attachment file size
        /// Arabic: حجم الملف المرفق
        /// </summary>
        public long? FileSize { get; set; }

        /// <summary>
        /// Attachment file type/extension
        /// Arabic: نوع الملف المرفق
        /// </summary>
        public string? FileType { get; set; }

        /// <summary>
        /// Attachment description
        /// Arabic: وصف المرفق
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Upload date
        /// Arabic: تاريخ الرفع
        /// </summary>
        public DateTime UploadedAt { get; set; }
    }
}
