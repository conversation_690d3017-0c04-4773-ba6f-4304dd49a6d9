namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for submitting assessment response
    /// Based on User Story 4: Respond to Assessment
    /// </summary>
    public record SubmitAssessmentResponseDto
    {
        /// <summary>
        /// Assessment ID to respond to
        /// Arabic: معرف التقييم للرد عليه
        /// </summary>
        public int AssessmentId { get; set; }

        /// <summary>
        /// Individual answers to questions
        /// Arabic: الإجابات الفردية على الأسئلة
        /// </summary>
        public List<SubmitAssessmentAnswerDto>? Answers { get; set; }

        /// <summary>
        /// Response attachments (for attachment-type assessments)
        /// Arabic: مرفقات الرد
        /// </summary>
        public List<int>? AttachmentIds { get; set; }

        /// <summary>
        /// General comments from the respondent
        /// Arabic: تعليقات عامة من المجيب
        /// </summary>
        public string? Comments { get; set; }

        /// <summary>
        /// Whether to save as draft (true) or submit final response (false)
        /// Arabic: حفظ كمسودة أم إرسال الرد النهائي
        /// </summary>
        public bool SaveAsDraft { get; set; } = false;
    }
}
