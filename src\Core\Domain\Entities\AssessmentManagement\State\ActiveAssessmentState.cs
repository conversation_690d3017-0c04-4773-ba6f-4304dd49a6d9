using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Represents an active assessment that has been distributed to board members
    /// Can transition to Completed state when all responses are collected
    /// Allows receiving responses and viewing results
    /// Based on User Stories 4, 5: Respond to Assessment, View Compiled Assessment Results
    /// </summary>
    public class ActiveAssessmentState : IAssessmentState
    {
        public AssessmentStatus Status => AssessmentStatus.Active;

        public void Handle(Assessment assessment)
        {
            // Active state - assessment is live and collecting responses
            // Check if all responses have been collected and mark as completed if so
            if (CanMarkAsCompleted(assessment))
            {
                // This would typically be handled by a background service or command handler
                // The state itself doesn't perform the transition, just indicates it's possible
            }
        }

        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return targetStatus == AssessmentStatus.Completed;
        }

        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return new[]
            {
                AssessmentStatus.Completed
            };
        }

        public bool CanEdit()
        {
            return false; // Active assessments cannot be edited
        }

        public bool CanDelete()
        {
            return false; // Active assessments cannot be deleted
        }

        public bool CanSubmitForApproval()
        {
            return false; // Already active
        }

        public bool CanApprove()
        {
            return false; // Already approved and active
        }

        public bool CanReject()
        {
            return false; // Cannot reject an active assessment
        }

        public bool CanDistribute()
        {
            return false; // Already distributed
        }

        public bool CanReceiveResponses()
        {
            return true; // Active assessments can receive responses from board members
        }

        public bool CanViewResults()
        {
            return true; // Active assessments can show real-time results
        }

        public bool CanMarkAsCompleted(Assessment assessment)
        {
            // Check if all board members have submitted their responses
            if (assessment?.Responses == null || !assessment.Responses.Any())
            {
                return false; // No responses yet
            }

            // Count total expected responses (board members for this fund)
            // This would typically be determined by the fund's board member count
            // For now, we check if all existing responses are completed
            var completedResponses = assessment.Responses.Count(r => r.Status == ResponseStatus.Completed);
            var totalResponses = assessment.Responses.Count();

            // Assessment can be completed if all responses are submitted
            // In a real implementation, this would check against the actual board member count
            return completedResponses == totalResponses && totalResponses > 0;
        }

        public IEnumerable<string> GetValidationMessages()
        {
            return new[]
            {
                "Assessment is active and collecting responses from board members",
                "Assessment results can be viewed in real-time",
                "Assessment will be automatically completed when all responses are collected"
            };
        }

        public string GetStateDescriptionKey()
        {
            return "AssessmentInActiveState";
        }

        public string GetStateDisplayNameKey()
        {
            return "AssessmentStatusActive";
        }
    }
}
