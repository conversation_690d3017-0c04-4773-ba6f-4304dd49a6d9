using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Dtos;
using Application.Base.Abstracts;
using AutoMapper;
using Domain.Entities.AssessmentManagement;
using Microsoft.Extensions.Localization;
using Resources;
using MediatR;

namespace Application.Features.AssessmentManagement.Queries.GetAssessmentDetails
{
    /// <summary>
    /// Handler for GetAssessmentDetailsQuery
    /// Retrieves comprehensive assessment details with complete hierarchy including responses and answers
    /// Follows CQRS pattern and existing architectural conventions
    /// Arabic: معالج استعلام الحصول على تفاصيل التقييم الشاملة
    /// </summary>
    public class GetAssessmentDetailsQueryHandler : BaseResponseHandler, IQueryHandler<GetAssessmentDetailsQuery, BaseResponse<ComprehensiveAssessmentDto>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetAssessmentDetailsQueryHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<ComprehensiveAssessmentDto>> Handle(GetAssessmentDetailsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting comprehensive assessment details for ID: {request.Id}");

                // 1. Validate current user authorization
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<ComprehensiveAssessmentDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Validate current user exists in database
                var currentUser = await _repository.User.GetByIdAsync<Domain.Entities.Users.User>(currentUserId.Value, trackChanges: false);
                if (currentUser == null)
                {
                    _logger.LogWarn($"Current user not found in database with ID: {currentUserId.Value}");
                    return Unauthorized<ComprehensiveAssessmentDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Get assessment with comprehensive data including responses and answers
                var assessment = await _repository.Assessment.GetAssessmentWithComprehensiveDetailsAsync(request.Id, trackChanges: false);
                if (assessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<ComprehensiveAssessmentDto>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // 4. Check user authorization to view comprehensive assessment details
                // TODO: Implement role-based authorization logic based on business requirements
                // For now, allowing all authenticated users to view comprehensive assessment details
                // This should be enhanced based on specific business rules

                // 5. Map to comprehensive DTO
                var comprehensiveDto = _mapper.Map<ComprehensiveAssessmentDto>(assessment);

                // 6. Calculate and set statistics
                if (comprehensiveDto.Statistics == null)
                {
                    comprehensiveDto.Statistics = new ComprehensiveStatisticsDto();
                }

                CalculateStatistics(assessment, comprehensiveDto.Statistics);

                _logger.LogInfo($"Successfully retrieved comprehensive assessment details for ID: {request.Id}");
                return Success(comprehensiveDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving comprehensive assessment details for ID: {request.Id}");
                return ServerError<ComprehensiveAssessmentDto>(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Calculates comprehensive statistics for the assessment
        /// </summary>
        private void CalculateStatistics(Assessment assessment, ComprehensiveStatisticsDto statistics)
        {
            // Calculate question statistics
            statistics.TotalQuestions = assessment.Questions?.Count ?? 0;

            // Calculate response statistics
            var responses = assessment.Responses?.ToList() ?? new List<AssessmentResponse>();
            statistics.TotalExpectedResponses = responses.Count;
            statistics.CompletedResponses = responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed);
            statistics.DraftResponses = responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Pending);

            // Calculate completion percentage
            if (statistics.TotalExpectedResponses > 0)
            {
                statistics.CompletionPercentage = Math.Round(
                    (decimal)statistics.CompletedResponses / statistics.TotalExpectedResponses * 100, 2);
            }

            // Calculate average response time (in minutes)
            var completedResponses = responses.Where(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed && r.SubmissionDate.HasValue).ToList();
            if (completedResponses.Any())
            {
                var totalMinutes = completedResponses.Sum(r =>
                {
                    var startTime = r.CreatedAt;
                    var endTime = r.SubmissionDate!.Value;
                    return (decimal)(endTime - startTime).TotalMinutes;
                });

                statistics.AverageResponseTime = Math.Round(totalMinutes / completedResponses.Count, 2);
            }
        }
        #endregion
    }
}
