using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Dtos;
using Application.Base.Abstracts;
using AutoMapper;
using Domain.Entities.AssessmentManagement;
using Microsoft.Extensions.Localization;
using Resources;
using MediatR;

namespace Application.Features.AssessmentManagement.Queries.GetById
{
    /// <summary>
    /// Handler for GetAssessmentByIdQuery
    /// Retrieves assessment details by ID with proper authorization and error handling
    /// Follows CQRS pattern and existing architectural conventions
    /// Arabic: معالج استعلام الحصول على تفاصيل التقييم حسب المعرف
    /// </summary>
    public class GetAssessmentByIdQueryHandler : BaseResponseHandler, IQueryHandler<GetAssessmentByIdQuery, BaseResponse<AssessmentByIdDto>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetAssessmentByIdQueryHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<AssessmentByIdDto>> Handle(GetAssessmentByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting assessment details for ID: {request.Id}");

                // 1. Validate current user authorization
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<AssessmentByIdDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Validate current user exists in database
                var currentUser = await _repository.User.GetByIdAsync<Domain.Entities.Users.User>(currentUserId.Value, trackChanges: false);
                if (currentUser == null)
                {
                    _logger.LogWarn($"Current user not found in database with ID: {currentUserId.Value}");
                    return Unauthorized<AssessmentByIdDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Get assessment with all related data
                var assessment = await _repository.Assessment.GetAssessmentWithDetailsAsync(request.Id, trackChanges: false);
                if (assessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<AssessmentByIdDto>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // 4. Check user authorization to view this assessment
                // TODO: Implement role-based authorization logic based on business requirements
                // For now, allowing all authenticated users to view assessments
                // This should be enhanced based on specific business rules

                // 5. Map to DTO
                var assessmentDto = _mapper.Map<AssessmentByIdDto>(assessment);

                _logger.LogInfo($"Successfully retrieved assessment details for ID: {request.Id}");
                return Success(assessmentDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving assessment details for ID: {request.Id}");
                return ServerError<AssessmentByIdDto>(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);
            }
        }
        #endregion
    }
}
