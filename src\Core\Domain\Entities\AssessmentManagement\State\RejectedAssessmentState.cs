using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Represents a rejected assessment that needs revision
    /// Can transition back to WaitingForApproval after revision
    /// Allows editing for revision purposes
    /// Based on User Story 2: Approve or Reject Assessment
    /// </summary>
    public class RejectedAssessmentState : IAssessmentState
    {
        public AssessmentStatus Status => AssessmentStatus.Rejected;

        public void Handle(Assessment assessment)
        {
            // Rejected state - assessment needs revision and resubmission
            // No specific handling required
        }

        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return targetStatus == AssessmentStatus.WaitingForApproval ||
                   targetStatus == AssessmentStatus.Draft; // Can be revised as draft
        }

        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return new[]
            {
                AssessmentStatus.WaitingForApproval,
                AssessmentStatus.Draft
            };
        }

        public bool CanEdit()
        {
            return true; // Rejected assessments can be edited for revision
        }

        public bool CanDelete()
        {
            return false; // Rejected assessments cannot be deleted (they have been reviewed)
        }

        public bool CanSubmitForApproval()
        {
            return true; // Rejected assessments can be resubmitted after revision
        }

        public bool CanApprove()
        {
            return false; // Cannot approve a rejected assessment directly
        }

        public bool CanReject()
        {
            return false; // Already rejected
        }

        public bool CanDistribute()
        {
            return false; // Rejected assessments cannot be distributed
        }

        public bool CanReceiveResponses()
        {
            return false; // Rejected assessments cannot receive responses
        }

        public bool CanViewResults()
        {
            return false; // No results available for rejected assessments
        }

        public bool CanMarkAsCompleted(Assessment assessment)
        {
            return false; // Rejected assessments cannot be marked as completed
        }

        public IEnumerable<string> GetValidationMessages()
        {
            return new[]
            {
                "Assessment has been rejected and needs revision",
                "Assessment can be edited and resubmitted for approval",
                "Check rejection reason for specific feedback"
            };
        }

        public string GetStateDescriptionKey()
        {
            return "AssessmentInRejectedState";
        }

        public string GetStateDisplayNameKey()
        {
            return "AssessmentStatusRejected";
        }
    }
}
