using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for Option entity
    /// Configures navigation properties, relationships, and constraints for question options
    /// Provides comprehensive configuration for predefined options in choice-based questions
    /// </summary>
    public class OptionEntityConfig : IEntityTypeConfiguration<Option>
    {
        public void Configure(EntityTypeBuilder<Option> builder)
        {
            // Configure primary key
            builder.HasKey(o => o.Id);

            // Configure required properties with constraints
            builder.Property(o => o.QuestionId)
                .IsRequired()
                .HasComment("Foreign key reference to the AssessmentQuestion this option belongs to");

            // Configure optional properties
            builder.Property(o => o.Value)
                .HasMaxLength(4000)
                .HasComment("The option value/text displayed to users");

            // Configure relationships
            builder.HasOne(o => o.Question)
                .WithMany(q => q.Options)
                .HasForeignKey(o => o.QuestionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Options_AssessmentQuestions_QuestionId");

            // Configure indexes for performance optimization
            builder.HasIndex(o => o.QuestionId)
                .HasDatabaseName("IX_Options_QuestionId_Performance");

            // Configure table name
            builder.ToTable("Options");

            // Configure audit properties (inherited from FullAuditedEntity)
            builder.Property(o => o.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the option was created");

            builder.Property(o => o.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the option was last updated");

            builder.Property(o => o.IsDeleted)
                .HasDefaultValue(false)
                .HasComment("Soft delete flag");

           
        }
    }
}
