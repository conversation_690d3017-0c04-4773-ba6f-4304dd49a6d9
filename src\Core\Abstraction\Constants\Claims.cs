﻿
namespace Abstraction.Constants
{
    public static partial class Claims
    {

        public static List<string> GenerateModules()
        {
            return new List<string>()
            {
                "Product",
                "Strategy",
                "DemoEntity",
                "Resolution",
                "BoardMember",
                "Document",
                "Assessment",
               // "Fund",
            };
        }
        public static List<string> GenerateFundManagerModules()
        {
            return new List<string>()
            {
                "Fund.View",
                "Fund.List",
                "Fund.Create",
                "Fund.EditFundExitdate",
                // Resolution permissions for Fund Manager
                "Resolution.View",
                "Resolution.List",
                "Resolution.Create",
                "Resolution.Edit",
                "Resolution.Delete",
                "Resolution.Cancel",
                // Board Member permissions for Fund Manager
                "BoardMember.Edit",
                "BoardMember.Delete",
                // Document permissions for Fund Manager
                "Document.View",
                "Document.List",
                "Document.Create",
                // Assessment permissions for Fund Manager
                "Assessment.Create",
                "Assessment.View",
                "Assessment.Edit",
                "Assessment.Delete",
                "Assessment.Distribute",
                "Assessment.ViewResults",
            };
        }
        public static List<string> GenerateBoardSecertaryModules()
        {
            return new List<string>()
            {
                "Fund.View",
                "Fund.List",
                //"Fund.Create",
                //"Fund.Edit",
                "Fund.EditFundExitdate",
                // Resolution permissions for Board Secretary (View only)
                "Resolution.View",
                "Resolution.List",
                 "Resolution.Edit",
                // Board Member permissions for Fund Manager
                "BoardMember.Create",
                // Document permissions for Board Secretary
                "Document.View",
                "Document.List",
                "Document.Create",
                // Assessment permissions for Board Secretary
                "Assessment.Approve",
                "Assessment.View",
                "Assessment.Reject",
                "Assessment.ViewResults",
            };
        }
        public static List<string> GenerateLegalCouncilModules()
        {
            return new List<string>()
            {

                "Fund.Edit",
                "Fund.View",
                "Fund.List",
                "Fund.Create",
                "Fund.Complete",
                "Fund.EditFundExitdate",
                // Resolution permissions for Legal Council (View only)
                "Resolution.View",
                "Resolution.List",
                "Resolution.Edit",
                // Board Member permissions for Fund Manager
                "BoardMember.Create",
                // Document permissions for Legal Council (View only)
                "Document.View",
                "Document.List",
                "Document.Delete",
                // Assessment permissions for Legal Council
                "Assessment.Approve",
                "Assessment.View",
                "Assessment.Reject",
                "Assessment.ViewResults",


    };
        }
        public static List<string> GenerateAdminModules()
        {
            return new List<string>()
            {

                "Strategy.Edit",
                "Strategy.View",
                "Strategy.List",
                "Strategy.Create",

    };
        }

        /// <summary>
        /// Generate modules and permissions for Board Member role
        /// Board Member can respond to assessments and view their personal assessments
        /// </summary>
        public static List<string> GenerateBoardMemberModules()
        {
            return new List<string>()
            {
                // Assessment permissions for Board Member
                "Assessment.Respond",
                "Assessment.ViewPersonal"
            };
        }
        public static List<string> GeneratePermissions(string module)
        {
            var permissions = new List<string>()
            {
                $"{module}.View",
                $"{module}.List",
                $"{module}.Create",
                $"{module}.Edit",
                $"{module}.Delete"
            };

            // Add Cancel permission for Resolution module
            if (module == "Resolution")
            {
                permissions.Add($"{module}.Cancel");
            }
            // Add Edit Fund Exitdate permission for Fund module
            if (module == "Fund")
            {
                permissions.Add($"{module}.EditFundExitdate");
            }
            // Add Assessment-specific permissions
            if (module == "Assessment")
            {
                permissions.Add($"{module}.Approve");
                permissions.Add($"{module}.Reject");
                permissions.Add($"{module}.Distribute");
                permissions.Add($"{module}.Respond");
                permissions.Add($"{module}.ViewResults");
                permissions.Add($"{module}.ViewPersonal");
            }


            return permissions;
        }

    }
}