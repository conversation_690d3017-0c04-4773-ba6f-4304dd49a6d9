using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Represents an assessment in draft status
    /// Can transition to WaitingForApproval state
    /// Allows editing and deletion operations
    /// Based on User Story 1: Create New Assessment
    /// </summary>
    public class DraftAssessmentState : IAssessmentState
    {
        public AssessmentStatus Status => AssessmentStatus.Draft;

        public void Handle(Assessment assessment)
        {
            // Draft state allows editing and can transition to waiting for approval
            // No specific handling required for draft state
        }

        public bool CanTransitionTo(AssessmentStatus targetStatus)
        {
            return targetStatus == AssessmentStatus.WaitingForApproval ||
                   targetStatus == AssessmentStatus.Draft; // Can save as draft again
        }

        public IEnumerable<AssessmentStatus> GetAllowedTransitions()
        {
            return new[]
            {
                AssessmentStatus.WaitingForApproval,
                AssessmentStatus.Draft
            };
        }

        public bool CanEdit()
        {
            return true; // Draft assessments can be edited
        }

        public bool CanDelete()
        {
            return true; // Draft assessments can be deleted
        }

        public bool CanSubmitForApproval()
        {
            return true; // Draft assessments can be submitted for approval
        }

        public bool CanApprove()
        {
            return false; // Draft assessments cannot be approved directly
        }

        public bool CanReject()
        {
            return false; // Draft assessments cannot be rejected
        }

        public bool CanDistribute()
        {
            return false; // Draft assessments cannot be distributed
        }

        public bool CanReceiveResponses()
        {
            return false; // Draft assessments cannot receive responses
        }

        public bool CanViewResults()
        {
            return false; // Draft assessments have no results to view
        }

        public bool CanMarkAsCompleted(Assessment assessment)
        {
            return false; // Draft assessments cannot be marked as completed
        }

        public IEnumerable<string> GetValidationMessages()
        {
            return new[]
            {
                "Assessment is in draft status and can be edited, deleted, or submitted for approval"
            };
        }

        public string GetStateDescriptionKey()
        {
            return "AssessmentInDraftState";
        }

        public string GetStateDisplayNameKey()
        {
            return "AssessmentStatusDraft";
        }
    }
}
