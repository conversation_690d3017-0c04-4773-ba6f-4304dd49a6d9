namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for creating assessment question options
    /// Arabic: كائل نقل البيانات لإنشاء خيارات أسئلة التقييم
    /// </summary>
    public record CreateAssessmentOptionDto
    {
        /// <summary>
        /// Option text
        /// Arabic: نص الخيار
        /// </summary>
        public string OptionText { get; set; } = string.Empty;

        /// <summary>
        /// Option order/sequence
        /// Arabic: ترتيب الخيار
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Whether this option is correct (for scoring purposes)
        /// Arabic: الخيار صحيح
        /// </summary>
        public bool IsCorrect { get; set; } = false;
    }
}
