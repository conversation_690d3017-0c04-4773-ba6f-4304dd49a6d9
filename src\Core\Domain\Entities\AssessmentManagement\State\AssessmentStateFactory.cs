using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Factory class for creating assessment state instances
    /// Implements the Factory pattern for state management
    /// Based on AssessmentStories.md requirements and Clean Architecture principles
    /// Follows the same pattern as ResolutionStateFactory for consistency
    /// </summary>
    public static class AssessmentStateFactory
    {
        /// <summary>
        /// Creates an assessment state instance based on the status enum
        /// </summary>
        /// <param name="status">The assessment status enum value</param>
        /// <returns>Appropriate IAssessmentState implementation</returns>
        /// <exception cref="ArgumentException">Thrown when status is not supported</exception>
        public static IAssessmentState CreateState(AssessmentStatus status)
        {
            return status switch
            {
                AssessmentStatus.Draft => new DraftAssessmentState(),
                AssessmentStatus.WaitingForApproval => new WaitingForApprovalAssessmentState(),
                AssessmentStatus.Approved => new ApprovedAssessmentState(),
                AssessmentStatus.Rejected => new RejectedAssessmentState(),
                AssessmentStatus.Active => new ActiveAssessmentState(),
                AssessmentStatus.Completed => new CompletedAssessmentState(),
                _ => throw new ArgumentException($"Unsupported assessment status: {status}", nameof(status))
            };
        }

        /// <summary>
        /// Gets the default initial state for a new assessment
        /// </summary>
        /// <param name="saveAsDraft">Whether the assessment is being saved as draft</param>
        /// <returns>Initial assessment state (Draft or WaitingForApproval)</returns>
        public static IAssessmentState GetInitialState(bool saveAsDraft = true)
        {
            return saveAsDraft ? new DraftAssessmentState() : new WaitingForApprovalAssessmentState();
        }

        /// <summary>
        /// Validates if a state transition is allowed
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <param name="targetStatus">Target assessment status</param>
        /// <returns>True if transition is allowed, false otherwise</returns>
        public static bool IsTransitionAllowed(AssessmentStatus currentStatus, AssessmentStatus targetStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanTransitionTo(targetStatus);
        }

        /// <summary>
        /// Gets all allowed transitions from a given status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>Collection of allowed target statuses</returns>
        public static IEnumerable<AssessmentStatus> GetAllowedTransitions(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.GetAllowedTransitions();
        }

        /// <summary>
        /// Validates if editing is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if editing is allowed</returns>
        public static bool CanEdit(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanEdit();
        }

        /// <summary>
        /// Validates if deletion is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if deletion is allowed</returns>
        public static bool CanDelete(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanDelete();
        }

        /// <summary>
        /// Validates if submission for approval is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if submission is allowed</returns>
        public static bool CanSubmitForApproval(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanSubmitForApproval();
        }

        /// <summary>
        /// Validates if approval is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if approval is allowed</returns>
        public static bool CanApprove(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanApprove();
        }

        /// <summary>
        /// Validates if rejection is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if rejection is allowed</returns>
        public static bool CanReject(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanReject();
        }

        /// <summary>
        /// Validates if distribution is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if distribution is allowed</returns>
        public static bool CanDistribute(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanDistribute();
        }

        /// <summary>
        /// Validates if receiving responses is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if receiving responses is allowed</returns>
        public static bool CanReceiveResponses(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanReceiveResponses();
        }

        /// <summary>
        /// Validates if viewing results is allowed for the current status
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <returns>True if viewing results is allowed</returns>
        public static bool CanViewResults(AssessmentStatus currentStatus)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanViewResults();
        }

        /// <summary>
        /// Validates if the assessment can be marked as completed
        /// </summary>
        /// <param name="currentStatus">Current assessment status</param>
        /// <param name="assessment">The assessment to validate</param>
        /// <returns>True if assessment can be marked as completed</returns>
        public static bool CanMarkAsCompleted(AssessmentStatus currentStatus, Assessment assessment)
        {
            var currentState = CreateState(currentStatus);
            return currentState.CanMarkAsCompleted(assessment);
        }
    }
}
