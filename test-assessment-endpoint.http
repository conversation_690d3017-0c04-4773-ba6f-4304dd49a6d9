### Test Assessment API Endpoint
### This file can be used with VS Code REST Client extension or similar tools

### 1. Test GET Assessment by ID - Valid Request
GET https://localhost:7001/api/assessments/1
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{token}}

### 2. Test GET Assessment by ID - Invalid ID (should return 400)
GET https://localhost:7001/api/assessments/0
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{token}}

### 3. Test GET Assessment by ID - Non-existent ID (should return 404)
GET https://localhost:7001/api/assessments/999999
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{token}}

### 4. Test GET Assessment by ID - Without Authorization (should return 401)
GET https://localhost:7001/api/assessments/1
Accept: application/json
Content-Type: application/json

### Expected Response Format for Valid Request:
### {
###   "data": {
###     "id": 1,
###     "fundId": 1,
###     "fundName": "Test Fund",
###     "title": "Assessment Title",
###     "description": "Assessment Description",
###     "dueDate": "2024-01-15T00:00:00Z",
###     "instructions": "Assessment Instructions",
###     "type": "Questionnaire", // or "Attachment"
###     "questions": [
###       {
###         "id": 1,
###         "questionText": "Question text",
###         "questionType": "MultipleChoice",
###         "displayOrder": 1,
###         "isRequired": true,
###         "options": [
###           {
###             "id": 1,
###             "value": "Option 1",
###             "order": 1,
###             "isCorrect": false
###           },
###           {
###             "id": 2,
###             "value": "Option 2", 
###             "order": 2,
###             "isCorrect": true
###           }
###         ]
###       }
###     ],
###     "attachment": null, // or attachment details for Attachment type
###     "createdAt": "2024-01-01T00:00:00Z",
###     "createdBy": 1,
###     "updatedAt": "2024-01-01T00:00:00Z",
###     "updatedBy": 1
###   },
###   "successed": true,
###   "statusCode": 200,
###   "message": null,
###   "errors": null
### }

### For Attachment type assessment, the response would include:
### {
###   "data": {
###     "id": 2,
###     "fundId": 1,
###     "fundName": "Test Fund",
###     "title": "Document Review Assessment",
###     "description": "Please review the attached document",
###     "dueDate": "2024-01-20T00:00:00Z",
###     "instructions": "Download and review the document, then provide feedback",
###     "type": "Attachment",
###     "questions": null,
###     "attachment": {
###       "id": 1,
###       "fileName": "document.pdf",
###       "fileSize": 1024000,
###       "contentType": "application/pdf",
###       "createdAt": "2024-01-01T00:00:00Z",
###       "createdBy": 1,
###       "updatedAt": "2024-01-01T00:00:00Z",
###       "updatedBy": 1
###     },
###     "createdAt": "2024-01-01T00:00:00Z",
###     "createdBy": 1,
###     "updatedAt": "2024-01-01T00:00:00Z",
###     "updatedBy": 1
###   },
###   "successed": true,
###   "statusCode": 200,
###   "message": null,
###   "errors": null
### }
