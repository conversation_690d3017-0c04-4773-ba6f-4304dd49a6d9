using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Repository;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Constants;

namespace Application.Features.AssessmentManagement.Commands.DeleteAssessment
{
    /// <summary>
    /// Handler for DeleteAssessmentCommand
    /// Implements soft delete functionality with proper authorization and audit trail
    /// Follows the same pattern as DeleteResolutionCommandHandler for consistency
    /// Arabic: معالج أمر حذف التقييم
    /// </summary>
    public class DeleteAssessmentCommandHandler : BaseResponseHandler, ICommandHandler<DeleteAssessmentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public DeleteAssessmentCommandHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<string>> Handle(DeleteAssessmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting DeleteAssessment operation for ID: {request.Id}");

                // 1. Validate request
                if (request == null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyRequestValidation]);

                // 2. Get assessment entity with details
                var assessment = await _repository.Assessment.GetAssessmentWithDetailsAsync(request.Id, trackChanges: true);
                if (assessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<string>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // 3. Get fund details for authorization
                var fundDetails = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {assessment.FundId}");
                    return NotFound<string>(_localizer[SharedResourcesKey.FundNotFound]);
                }

                // 4. Validate current user authorization
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 5. Validate user permissions (only fund managers can delete assessments)
                if (!await HasDeletePermission(fundDetails, currentUserId))
                {
                    _logger.LogWarn($"User {currentUserId} does not have permission to delete assessment {request.Id}");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 6. Validate assessment can be deleted (only draft assessments can be deleted)
                if (assessment.Status != AssessmentStatus.Draft)
                {
                    _logger.LogWarn($"Cannot delete assessment {request.Id} with status {assessment.Status}. Only draft assessments can be deleted.");
                    return BadRequest<string>(_localizer[SharedResourcesKey.AssessmentCannotBeDeleted]);
                }

                // 7. Add comprehensive audit entry before deletion
                var currentUserName = _currentUserService.UserName;
                var localizationKey = SharedResourcesKey.AuditActionAssessmentDeletion;

                // Initialize state pattern if not already initialized
                if (assessment.StateContext == null)
                {
                    assessment.InitializeState();
                }

                var comprehensiveDetails = $"Assessment '{assessment.Title}' deleted by Fund Manager: {currentUserName}. Assessment was in {assessment.Status} status and contained {assessment.Questions?.Count ?? 0} questions.";
                assessment.StateContext.AddAuditEntry(AssessmentActionEnum.Delete, "Assessment deleted", localizationKey, currentUserId.Value, Roles.FundManager.ToString(), comprehensiveDetails);

                // 8. Soft delete assessment (mark as deleted)
                assessment.IsDeleted = true;
                assessment.DeletedAt = DateTime.UtcNow;
                assessment.DeletedBy = currentUserId.Value;

                var deleteResult = await _repository.Assessment.UpdateAsync(assessment);
                if (!deleteResult)
                {
                    _logger.LogError(null, $"Failed to delete assessment with ID: {request.Id}");
                    return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorDeletingData]);
                }

                _logger.LogInfo($"Assessment deleted successfully with ID: {request.Id}");
                return Success<string>(_localizer[SharedResourcesKey.ItemDeletedSuccessfully]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in DeleteAssessment for ID: {request.Id}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorDeletingData]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Checks if the current user has permission to delete the assessment
        /// Only Fund Managers can delete assessments
        /// </summary>
        private async Task<bool> HasDeletePermission(Fund fundDetails, int? currentUserId)
        {
            if (!currentUserId.HasValue || fundDetails == null)
                return false;

            try
            {
                // Check if user is a Fund Manager for this fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId.Value);
                    if (isFundManager)
                    {
                        _logger.LogInfo($"User ID: {currentUserId.Value} is Fund Manager for Fund ID: {fundDetails.Id}");
                        return true;
                    }
                }

                _logger.LogWarn($"User ID: {currentUserId.Value} does not have Fund Manager role for Fund ID: {fundDetails.Id}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking delete permissions for User ID: {currentUserId.Value} and Fund ID: {fundDetails.Id}");
                return false;
            }
        }
        #endregion
    }
}
