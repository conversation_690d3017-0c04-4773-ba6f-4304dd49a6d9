using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities.AssessmentManagement;
using Abstraction.Contracts.Repository;
using Domain.Entities.FundManagement;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Constants;
using Domain.Entities.Notifications;

namespace Application.Features.AssessmentManagement.Commands.CreateAssessment
{
    /// <summary>
    /// Handler for CreateAssessmentCommand
    /// Implements comprehensive assessment creation with business rules, validation, and notifications
    /// Based on User Story 1: Create New Assessment
    /// Follows the same pattern as AddResolutionCommandHandler for consistency
    /// </summary>
    public class CreateAssessmentCommandHandler : BaseResponseHandler, ICommandHandler<CreateAssessmentCommand, BaseResponse<string>>
    {

        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructors
        public CreateAssessmentCommandHandler(
            IRepositoryManager repository,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(CreateAssessmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting CreateAssessment operation for FundId: {request.FundId}");

                // 1. Validate request
                if (request == null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);

                // 2. Validate current user authorization
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Validate fund exists and is active
                var fund = await _repository.Funds.GetByIdAsync<Fund>(request.FundId, trackChanges: false);
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {request.FundId}");
                    return NotFound<string>(_localizer[SharedResourcesKey.FundNotFound]);
                }

                // 4. Validate user has permission to create assessment for this fund
                if (!await HasCreatePermission(fund, currentUserId.Value))
                {
                    _logger.LogWarn($"User {currentUserId} does not have permission to create assessment for fund {request.FundId}");
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 5. Create assessment entity
                var assessment = _mapper.Map<Assessment>(request);
                assessment.CreatedBy = currentUserId.Value; // Use the validated user ID

                // Get user context for comprehensive audit logging
                var currentUserName = _currentUserService.UserName ?? "Unknown User";

                _logger.LogInfo($"Creating assessment with CreatedBy: {assessment.CreatedBy}, User: {currentUserName}");

                // 6. Initialize state based on SaveAsDraft flag
                assessment.InitializeState();

                if (request.SaveAsDraft)
                {
                    // Keep as Draft status - no status transition needed
                    _logger.LogInfo($"Assessment created in Draft status by Fund Manager: {currentUserName}");
                }
                else
                {
                    // Transition to WaitingForApproval status
                    assessment.Status = Domain.Entities.AssessmentManagement.Enums.AssessmentStatus.WaitingForApproval;
                    _logger.LogInfo($"Assessment created and sent for review by Fund Manager: {currentUserName}");
                }

                // 7. Save assessment
                var addedAssessment = await _repository.Assessment.AddAsync(assessment, cancellationToken);
                _logger.LogInfo($"Assessment created successfully with ID: {addedAssessment.Id}");

                // 8. Send notifications based on flow type
                if (!request.SaveAsDraft)
                {
                    // Send notifications if not saved as draft
                    await AddNotification(fund.Id, addedAssessment);
                }

                // 9. Return success response
                string successMessage = request.SaveAsDraft
                    ? _localizer[SharedResourcesKey.AssessmentSavedAsDraftSuccessfully] // Using existing key for now
                    : _localizer[SharedResourcesKey.AssessmentAddedSuccessfully]; // Using existing key for now

                return Success<string>(successMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in CreateAssessment: {ex.Message}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Validates if the current user has permission to create an assessment for the specified fund
        /// Ensures that only fund managers can create assessments for their funds
        /// </summary>
        /// <param name="fund">The fund for which the assessment is being created</param>
        /// <param name="currentUserId">The current user ID</param>
        /// <returns>True if user has permission to create assessment, false otherwise</returns>
        private async Task<bool> HasCreatePermission(Fund fund, int currentUserId)
        {
            // Get fund details to check if user is a fund manager
            var fundDetails = await _repository.Funds.ViewFundUsers(fund.Id, trackChanges: false);
            if (fundDetails?.FundManagers != null)
            {
                return fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
            }
            return false;
        }

        /// <summary>
        /// Adds notifications for assessment creation following AddResolutionCommandHandler pattern
        /// Based on assessment submission notification requirements
        /// </summary>
        private async Task AddNotification(int fundId, Assessment assessment)
        {
            var notifications = new List<Domain.Entities.Notifications.Notification>();

            // Get fund details to access legal council and board secretaries
            var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
            if (fundDetails == null) return;

            // Notify Legal Council attached to the fund
            if (fundDetails.LegalCouncilId > 0)
            {
                notifications.Add(new Domain.Entities.Notifications.Notification
                {
                    Title = string.Empty,
                    Body = $"{fundDetails.Name}|{_currentUserService.UserName}",
                    FundId = fundDetails.Id,
                    UserId = fundDetails.LegalCouncilId,
                    NotificationType =(int) NotificationType.AssessmentCreated,
                    NotificationModule = (int)NotificationModule.Evaluations
                });
            }

            // Notify Board Secretaries attached to the fund
            var boardSecretaries = fundDetails.FundBoardSecretaries ?? new List<FundBoardSecretary>();
            foreach (var boardSecretary in boardSecretaries)
            {
                notifications.Add(new Domain.Entities.Notifications.Notification
                {
                    Title = string.Empty,
                    Body = $"{fundDetails.Name}|{_currentUserService.UserName}",
                    FundId = fundDetails.Id,
                    UserId = boardSecretary.UserId,
                    NotificationType = (int)NotificationType.AssessmentCreated, 
                    NotificationModule = (int)NotificationModule.Evaluations
                });
            }

            if (notifications.Any())
            {
                await _repository.Notifications.AddRangeAsync(notifications);
                _logger.LogInfo($"Assessment submission notifications added for Assessment ID: {assessment.Id}, Count: {notifications.Count}");
            }
        }
        #endregion

    }
}
