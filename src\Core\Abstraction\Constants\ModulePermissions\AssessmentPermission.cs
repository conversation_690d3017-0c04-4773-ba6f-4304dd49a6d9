﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class AssessmentPermission
    {
        public const string View = "Assessment.View";
        public const string List = "Assessment.List";
        public const string Create = "Assessment.Create";
        public const string Edit = "Assessment.Edit";
        public const string Delete = "Assessment.Delete";
        public const string Approve = "Assessment.Approve";
        public const string Reject = "Assessment.Reject";
    }
}
