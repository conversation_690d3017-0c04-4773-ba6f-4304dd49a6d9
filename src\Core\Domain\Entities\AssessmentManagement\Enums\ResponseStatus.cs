﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.Enums
{
    /// <summary>
    /// Enumeration representing the status of an assessment response
    /// Based on user story requirements
    /// </summary>
    public enum ResponseStatus
    {
        /// <summary>
        /// Response is pending - board member hasn't submitted yet
        /// Arabic: معلق
        /// </summary>
        Pending = 1,

        /// <summary>
        /// Response is completed - board member has submitted
        /// Arabic: مكتمل
        /// </summary>
        Completed = 2
    }
}
