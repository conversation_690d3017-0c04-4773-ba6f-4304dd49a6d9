﻿using Abstraction.Contracts.Repository.Resolution;
using Domain.Entities.ResolutionManagement;

namespace Infrastructure.Data.Config
{

    public static class ResolutionTypeSeed
    {
        public static async Task SeedResolutionTypeAsync(IResolutionTypeRepository resolutionTypeRepository)
        {
            var resolutionTypes = new List<ResolutionType>
            {
                new ResolutionType
                {
                    
                    NameAr = "استحواذ",
                    NameEn = "Acquisition",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 1,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                    
                    NameAr = "تخارج",
                    NameEn = "Exit",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 2,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                   
                    NameAr = "بيع",
                    NameEn = "Sell",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 3,
                    CreatedAt =DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                   
                    NameAr = "توزيع أرباح",
                    NameEn = "ProfitDistribution",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 4,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                 
                    NameAr = "تمديد مدة الصندوق",
                    NameEn = "FundDurationExtension",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 5,
                    CreatedAt =DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                   
                    NameAr = "تعديل شروط وأحكام الصندوق",
                    NameEn = "TermsAndConditionsModification",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 6,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                     
                    NameAr = "الموافقة على القوائم المالية",
                    NameEn = "FinancialStatementsApproval",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 7,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                   
                    NameAr = "تعيين مقدمي خدمات",
                    NameEn = "ServiceProviderAssignment",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 8,
                    CreatedAt =DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                    
                    NameAr = "الموافقة على شروط وأحكام الصندوق",
                    NameEn = "TermsAndConditionsApproval",
                    IsActive = true,
                    IsOther = false,
                    DisplayOrder = 9,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                },
                new ResolutionType
                {
                     
                    NameAr = "أخرى",
                    NameEn = "Other",
                    IsActive = true,
                    IsOther = true,
                    DisplayOrder = 10,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1,
                    UpdatedAt = null,
                    UpdatedBy = null,
                    DeletedAt = null,
                    IsDeleted = false,
                    DeletedBy = null
                }
            };
            foreach (var resolutionType in resolutionTypes)
            {
                if (!await resolutionTypeRepository.AnyAsync<ResolutionType>(x => x.NameEn == resolutionType.NameEn))
                {
                    await resolutionTypeRepository.AddAsync(resolutionType);
                }
            }

        }
    }
}

