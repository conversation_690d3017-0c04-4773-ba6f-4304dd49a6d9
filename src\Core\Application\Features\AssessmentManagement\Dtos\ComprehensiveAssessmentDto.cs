using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// Comprehensive DTO for Assessment details with complete hierarchy
    /// Contains assessment, questions, options, responses, answers, and answer options
    /// Used for detailed assessment review and analysis
    /// Arabic: كائل نقل البيانات الشامل لتفاصيل التقييم مع التسلسل الهرمي الكامل
    /// </summary>
    public record ComprehensiveAssessmentDto : BaseDto
    {
        /// <summary>
        /// Fund information
        /// Arabic: معلومات الصندوق
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Fund name
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;

        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment type (Questionnaire or Attachment)
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Assessment due date
        /// Arabic: تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Assessment instructions
        /// Arabic: تعليمات التقييم
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Whether anonymous responses are allowed
        /// Arabic: السماح بالردود المجهولة
        /// </summary>
        public bool AllowAnonymousResponses { get; set; }

        /// <summary>
        /// Whether responses can be edited after submission
        /// Arabic: السماح بتعديل الردود
        /// </summary>
        public bool AllowResponseEditing { get; set; }

        /// <summary>
        /// Reviewer information (if reviewed)
        /// Arabic: معلومات المراجع
        /// </summary>
        public int? ReviewedBy { get; set; }

        /// <summary>
        /// Reviewer name
        /// Arabic: اسم المراجع
        /// </summary>
        public string? ReviewerName { get; set; }

        /// <summary>
        /// Review date
        /// Arabic: تاريخ المراجعة
        /// </summary>
        public DateTime? ReviewedDate { get; set; }

        /// <summary>
        /// Reviewer comments
        /// Arabic: تعليقات المراجع
        /// </summary>
        public string? ReviewerComments { get; set; }

        /// <summary>
        /// Assessment questions (for Questionnaire type)
        /// Arabic: أسئلة التقييم
        /// </summary>
        public List<ComprehensiveQuestionDto>? Questions { get; set; }

        /// <summary>
        /// Assessment attachment (for Attachment type)
        /// Arabic: مرفق التقييم
        /// </summary>
        public ComprehensiveAttachmentDto? Attachment { get; set; }

        /// <summary>
        /// All responses submitted for this assessment
        /// Arabic: جميع الردود المقدمة لهذا التقييم
        /// </summary>
        public List<ComprehensiveResponseDto>? Responses { get; set; }

        /// <summary>
        /// Assessment statistics
        /// Arabic: إحصائيات التقييم
        /// </summary>
        public ComprehensiveStatisticsDto? Statistics { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Question details with options
    /// Arabic: كائل نقل البيانات لتفاصيل سؤال التقييم مع الخيارات
    /// </summary>
    public record ComprehensiveQuestionDto : BaseDto
    {
        /// <summary>
        /// Question text
        /// Arabic: نص السؤال
        /// </summary>
        public string QuestionText { get; set; } = string.Empty;

        /// <summary>
        /// Question type
        /// Arabic: نوع السؤال
        /// </summary>
        public QuestionType QuestionType { get; set; }

        /// <summary>
        /// Question display order
        /// Arabic: ترتيب عرض السؤال
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Whether the question is required
        /// Arabic: السؤال مطلوب
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Question options (for choice-type questions)
        /// Arabic: خيارات السؤال
        /// </summary>
        public List<ComprehensiveOptionDto>? Options { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Option details
    /// Arabic: كائل نقل البيانات لتفاصيل خيار التقييم
    /// </summary>
    public record ComprehensiveOptionDto : BaseDto
    {
        /// <summary>
        /// Option text/value
        /// Arabic: نص الخيار
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Option display order
        /// Arabic: ترتيب عرض الخيار
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Whether this option is correct
        /// Arabic: الخيار صحيح
        /// </summary>
        public bool IsCorrect { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Response details with answers
    /// Arabic: كائل نقل البيانات لتفاصيل رد التقييم مع الإجابات
    /// </summary>
    public record ComprehensiveResponseDto : BaseDto
    {
        /// <summary>
        /// User ID of the respondent
        /// Arabic: معرف المستخدم المجيب
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Respondent name
        /// Arabic: اسم المجيب
        /// </summary>
        public string? RespondentName { get; set; }

        /// <summary>
        /// Response status
        /// Arabic: حالة الرد
        /// </summary>
        public ResponseStatus Status { get; set; }

        /// <summary>
        /// Whether this is an anonymous response
        /// Arabic: رد مجهول
        /// </summary>
        public bool IsAnonymous { get; set; }

        /// <summary>
        /// Response submission date
        /// Arabic: تاريخ تقديم الرد
        /// </summary>
        public DateTime? SubmissionDate { get; set; }

        /// <summary>
        /// Individual answers for each question
        /// Arabic: الإجابات الفردية لكل سؤال
        /// </summary>
        public List<ComprehensiveAnswerDto>? Answers { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Answer details with selected options
    /// Arabic: كائل نقل البيانات لتفاصيل إجابة التقييم مع الخيارات المختارة
    /// </summary>
    public record ComprehensiveAnswerDto : BaseDto
    {
        /// <summary>
        /// Question ID this answer belongs to
        /// Arabic: معرف السؤال
        /// </summary>
        public int QuestionId { get; set; }

        /// <summary>
        /// Question text for reference
        /// Arabic: نص السؤال للمرجع
        /// </summary>
        public string? QuestionText { get; set; }

        /// <summary>
        /// Text answer (for text-type questions)
        /// Arabic: إجابة نصية
        /// </summary>
        public string? TextAnswer { get; set; }

        /// <summary>
        /// Selected options (for choice-type questions)
        /// Arabic: الخيارات المختارة
        /// </summary>
        public List<ComprehensiveAnswerOptionDto>? SelectedOptions { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Answer Option details
    /// Arabic: كائل نقل البيانات لتفاصيل خيار إجابة التقييم
    /// </summary>
    public record ComprehensiveAnswerOptionDto : BaseDto
    {
        /// <summary>
        /// Option ID that was selected
        /// Arabic: معرف الخيار المختار
        /// </summary>
        public int OptionId { get; set; }

        /// <summary>
        /// Option text for display
        /// Arabic: نص الخيار للعرض
        /// </summary>
        public string? OptionText { get; set; }

        /// <summary>
        /// Whether this option is correct (for scoring)
        /// Arabic: الخيار صحيح
        /// </summary>
        public bool IsCorrect { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Attachment details
    /// Arabic: كائل نقل البيانات لتفاصيل مرفق التقييم
    /// </summary>
    public record ComprehensiveAttachmentDto : BaseDto
    {
        /// <summary>
        /// Original file name
        /// Arabic: اسم الملف الأصلي
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// Arabic: حجم الملف بالبايت
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// File content type (MIME type)
        /// Arabic: نوع محتوى الملف
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// File upload date
        /// Arabic: تاريخ رفع الملف
        /// </summary>
        public DateTime? UploadedAt { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Statistics
    /// Arabic: كائل نقل البيانات لإحصائيات التقييم
    /// </summary>
    public record ComprehensiveStatisticsDto
    {
        /// <summary>
        /// Total number of questions
        /// Arabic: العدد الإجمالي للأسئلة
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// Total expected responses
        /// Arabic: العدد المتوقع للردود
        /// </summary>
        public int TotalExpectedResponses { get; set; }

        /// <summary>
        /// Number of completed responses
        /// Arabic: عدد الردود المكتملة
        /// </summary>
        public int CompletedResponses { get; set; }

        /// <summary>
        /// Number of draft responses
        /// Arabic: عدد الردود المسودة
        /// </summary>
        public int DraftResponses { get; set; }

        /// <summary>
        /// Completion percentage
        /// Arabic: نسبة الإنجاز
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Average response time in minutes
        /// Arabic: متوسط وقت الرد بالدقائق
        /// </summary>
        public decimal? AverageResponseTime { get; set; }
    }
}
