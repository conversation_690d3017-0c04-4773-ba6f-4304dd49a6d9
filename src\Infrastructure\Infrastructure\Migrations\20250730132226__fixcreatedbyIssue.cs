﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class _fixcreatedbyIssue : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_CreatedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_DeletedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_UpdatedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_Users_CreatedBy",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_Users_DeletedBy",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_Users_UpdatedBy",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_AspNetUsers_CreatedByUserId",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_AspNetUsers_DeletedByUserId",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_AspNetUsers_UpdatedByUserId",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_Users_CreatedBy",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_Users_DeletedBy",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_Users_UpdatedBy",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_CreatedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_DeletedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_UpdatedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_Users_CreatedBy",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_Users_DeletedBy",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_Users_UpdatedBy",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_CreatedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_DeletedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_UpdatedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_Users_CreatedBy",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_Users_DeletedBy",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_Users_UpdatedBy",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_AspNetUsers_CreatedByUserId",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_AspNetUsers_DeletedByUserId",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_AspNetUsers_UpdatedByUserId",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_Users_CreatedBy",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_Users_DeletedBy",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_Users_UpdatedBy",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_AspNetUsers_CreatedByUserId",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_AspNetUsers_DeletedByUserId",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_AspNetUsers_UpdatedByUserId",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_Users_CreatedBy",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_Users_DeletedBy",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_Users_UpdatedBy",
                table: "Options");

            migrationBuilder.DropIndex(
                name: "IX_Options_CreatedByUserId",
                table: "Options");

            migrationBuilder.DropIndex(
                name: "IX_Options_DeletedByUserId",
                table: "Options");

            migrationBuilder.DropIndex(
                name: "IX_Options_UpdatedByUserId",
                table: "Options");

            migrationBuilder.DropIndex(
                name: "IX_Assessments_CreatedByUserId",
                table: "Assessments");

            migrationBuilder.DropIndex(
                name: "IX_Assessments_DeletedByUserId",
                table: "Assessments");

            migrationBuilder.DropIndex(
                name: "IX_Assessments_UpdatedByUserId",
                table: "Assessments");

            migrationBuilder.DropIndex(
                name: "IX_AssessmentResponses_CreatedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropIndex(
                name: "IX_AssessmentResponses_DeletedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropIndex(
                name: "IX_AssessmentResponses_UpdatedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropIndex(
                name: "IX_AssessmentQuestions_CreatedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropIndex(
                name: "IX_AssessmentQuestions_DeletedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropIndex(
                name: "IX_AssessmentQuestions_UpdatedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropIndex(
                name: "IX_Answers_CreatedByUserId",
                table: "Answers");

            migrationBuilder.DropIndex(
                name: "IX_Answers_DeletedByUserId",
                table: "Answers");

            migrationBuilder.DropIndex(
                name: "IX_Answers_UpdatedByUserId",
                table: "Answers");

            migrationBuilder.DropIndex(
                name: "IX_AnswerOptions_CreatedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropIndex(
                name: "IX_AnswerOptions_DeletedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropIndex(
                name: "IX_AnswerOptions_UpdatedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "Options");

            migrationBuilder.DropColumn(
                name: "DeletedByUserId",
                table: "Options");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "Options");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "DeletedByUserId",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropColumn(
                name: "DeletedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "AssessmentResponses");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropColumn(
                name: "DeletedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "AssessmentQuestions");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "Answers");

            migrationBuilder.DropColumn(
                name: "DeletedByUserId",
                table: "Answers");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "Answers");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropColumn(
                name: "DeletedByUserId",
                table: "AnswerOptions");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "AnswerOptions");

            migrationBuilder.AlterColumn<int>(
                name: "CreatedBy",
                table: "AssessmentStatusHistories",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "User ID who created this status history entry");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "AssessmentStatusHistories",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()",
                oldComment: "Timestamp when the status history entry was created");

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_CreatedBy",
                table: "AnswerOptions",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_DeletedBy",
                table: "AnswerOptions",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_UpdatedBy",
                table: "AnswerOptions",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_AspNetUsers_CreatedBy",
                table: "Answers",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_AspNetUsers_DeletedBy",
                table: "Answers",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_AspNetUsers_UpdatedBy",
                table: "Answers",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_CreatedBy",
                table: "AssessmentQuestions",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_DeletedBy",
                table: "AssessmentQuestions",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_UpdatedBy",
                table: "AssessmentQuestions",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_CreatedBy",
                table: "AssessmentResponses",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_DeletedBy",
                table: "AssessmentResponses",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_UpdatedBy",
                table: "AssessmentResponses",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_AspNetUsers_CreatedBy",
                table: "Assessments",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_AspNetUsers_DeletedBy",
                table: "Assessments",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_AspNetUsers_UpdatedBy",
                table: "Assessments",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Options_AspNetUsers_CreatedBy",
                table: "Options",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Options_AspNetUsers_DeletedBy",
                table: "Options",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Options_AspNetUsers_UpdatedBy",
                table: "Options",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_CreatedBy",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_DeletedBy",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_UpdatedBy",
                table: "AnswerOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_AspNetUsers_CreatedBy",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_AspNetUsers_DeletedBy",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_Answers_AspNetUsers_UpdatedBy",
                table: "Answers");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_CreatedBy",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_DeletedBy",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_UpdatedBy",
                table: "AssessmentQuestions");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_CreatedBy",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_DeletedBy",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_UpdatedBy",
                table: "AssessmentResponses");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_AspNetUsers_CreatedBy",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_AspNetUsers_DeletedBy",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_AspNetUsers_UpdatedBy",
                table: "Assessments");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_AspNetUsers_CreatedBy",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_AspNetUsers_DeletedBy",
                table: "Options");

            migrationBuilder.DropForeignKey(
                name: "FK_Options_AspNetUsers_UpdatedBy",
                table: "Options");

            migrationBuilder.AddColumn<int>(
                name: "CreatedByUserId",
                table: "Options",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DeletedByUserId",
                table: "Options",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedByUserId",
                table: "Options",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "CreatedBy",
                table: "AssessmentStatusHistories",
                type: "int",
                nullable: false,
                comment: "User ID who created this status history entry",
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "AssessmentStatusHistories",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                comment: "Timestamp when the status history entry was created",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AddColumn<int>(
                name: "CreatedByUserId",
                table: "Assessments",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DeletedByUserId",
                table: "Assessments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedByUserId",
                table: "Assessments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CreatedByUserId",
                table: "AssessmentResponses",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DeletedByUserId",
                table: "AssessmentResponses",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedByUserId",
                table: "AssessmentResponses",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CreatedByUserId",
                table: "AssessmentQuestions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DeletedByUserId",
                table: "AssessmentQuestions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedByUserId",
                table: "AssessmentQuestions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CreatedByUserId",
                table: "Answers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DeletedByUserId",
                table: "Answers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedByUserId",
                table: "Answers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CreatedByUserId",
                table: "AnswerOptions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DeletedByUserId",
                table: "AnswerOptions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedByUserId",
                table: "AnswerOptions",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Options_CreatedByUserId",
                table: "Options",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Options_DeletedByUserId",
                table: "Options",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Options_UpdatedByUserId",
                table: "Options",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_CreatedByUserId",
                table: "Assessments",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_DeletedByUserId",
                table: "Assessments",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_UpdatedByUserId",
                table: "Assessments",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_CreatedByUserId",
                table: "AssessmentResponses",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_DeletedByUserId",
                table: "AssessmentResponses",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentResponses_UpdatedByUserId",
                table: "AssessmentResponses",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_CreatedByUserId",
                table: "AssessmentQuestions",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_DeletedByUserId",
                table: "AssessmentQuestions",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AssessmentQuestions_UpdatedByUserId",
                table: "AssessmentQuestions",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_CreatedByUserId",
                table: "Answers",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_DeletedByUserId",
                table: "Answers",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Answers_UpdatedByUserId",
                table: "Answers",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_CreatedByUserId",
                table: "AnswerOptions",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_DeletedByUserId",
                table: "AnswerOptions",
                column: "DeletedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AnswerOptions_UpdatedByUserId",
                table: "AnswerOptions",
                column: "UpdatedByUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_CreatedByUserId",
                table: "AnswerOptions",
                column: "CreatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_DeletedByUserId",
                table: "AnswerOptions",
                column: "DeletedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_AspNetUsers_UpdatedByUserId",
                table: "AnswerOptions",
                column: "UpdatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_Users_CreatedBy",
                table: "AnswerOptions",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_Users_DeletedBy",
                table: "AnswerOptions",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AnswerOptions_Users_UpdatedBy",
                table: "AnswerOptions",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_AspNetUsers_CreatedByUserId",
                table: "Answers",
                column: "CreatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_AspNetUsers_DeletedByUserId",
                table: "Answers",
                column: "DeletedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_AspNetUsers_UpdatedByUserId",
                table: "Answers",
                column: "UpdatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_Users_CreatedBy",
                table: "Answers",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_Users_DeletedBy",
                table: "Answers",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Answers_Users_UpdatedBy",
                table: "Answers",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_CreatedByUserId",
                table: "AssessmentQuestions",
                column: "CreatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_DeletedByUserId",
                table: "AssessmentQuestions",
                column: "DeletedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_AspNetUsers_UpdatedByUserId",
                table: "AssessmentQuestions",
                column: "UpdatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_Users_CreatedBy",
                table: "AssessmentQuestions",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_Users_DeletedBy",
                table: "AssessmentQuestions",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentQuestions_Users_UpdatedBy",
                table: "AssessmentQuestions",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_CreatedByUserId",
                table: "AssessmentResponses",
                column: "CreatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_DeletedByUserId",
                table: "AssessmentResponses",
                column: "DeletedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_AspNetUsers_UpdatedByUserId",
                table: "AssessmentResponses",
                column: "UpdatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_Users_CreatedBy",
                table: "AssessmentResponses",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_Users_DeletedBy",
                table: "AssessmentResponses",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_Users_UpdatedBy",
                table: "AssessmentResponses",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_AspNetUsers_CreatedByUserId",
                table: "Assessments",
                column: "CreatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_AspNetUsers_DeletedByUserId",
                table: "Assessments",
                column: "DeletedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_AspNetUsers_UpdatedByUserId",
                table: "Assessments",
                column: "UpdatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_Users_CreatedBy",
                table: "Assessments",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_Users_DeletedBy",
                table: "Assessments",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_Users_UpdatedBy",
                table: "Assessments",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Options_AspNetUsers_CreatedByUserId",
                table: "Options",
                column: "CreatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Options_AspNetUsers_DeletedByUserId",
                table: "Options",
                column: "DeletedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Options_AspNetUsers_UpdatedByUserId",
                table: "Options",
                column: "UpdatedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Options_Users_CreatedBy",
                table: "Options",
                column: "CreatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Options_Users_DeletedBy",
                table: "Options",
                column: "DeletedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Options_Users_UpdatedBy",
                table: "Options",
                column: "UpdatedBy",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }
    }
}
