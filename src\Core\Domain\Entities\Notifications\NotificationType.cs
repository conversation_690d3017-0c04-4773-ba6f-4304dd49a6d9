﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.Notifications
{
    public enum NotificationType
    {
        AddedToFund = 1,
        RemoveFromFund = 2,
        ChangeExitDate = 3,
        CompeleteFund = 4,
        ResolutionCreated = 5, // MSG002 - Resolution created notification
        ResolutionUpdated = 6, // MSG005 - Resolution updated notification
        FundActivated = 7,     // MSG008 - Fund activated notification
        BoardMemberAdded = 8,  // MSG002 - Board member added notification (to the new member)
        BoardMemberAddedToFund = 9, // MSG007 - Board member added notification (to fund stakeholders)
        ResolutionCancelled = 10, // MSG004 - Resolution cancelled notification
        ResolutionConfirmed = 11, // MSG002 - Resolution confirmed notification
        ResolutionRejected = 12,  // MSG004 - Resolution rejected notification
        ResolutionSentToVote = 13, // MSG002 - Resolution sent to vote notification
        NewResolutionCreatedFromApproved = 14, // MSG009 - New resolution created from approved/not approved
        ResolutionVotingSuspended = 15, // MSG007 - Resolution voting suspended notification (Alternative 1)
        ResolutionDataCompleted = 16, // MSG003 - Resolution data completed notification (JDWA-507)
        AddedToFundForManager = 17,
        UserRelieveOfDuties = 18, // MSG-EDIT-013 - User relieve of duties notification
        UserRoleUpdate = 19, // MSG-EDIT-014 - User role update notification
        SessionActivityReminder = 20,
        SessionExpiredReminder = 21,
        DocumentAdded = 22, // MSG-N-01 - Document added notification
        DocumentDeleted = 23, // MSG-N-03 - Document deleted notification

        // Assessment Management Notifications
        AssessmentCreated = 24, // MSG-ASM-002 - Assessment submitted for approval notification
        AssessmentApproved = 25, // MSG-ASM-005 - Assessment approved notification
        AssessmentRejected = 26, // MSG-ASM-006 - Assessment rejected notification
        AssessmentDistributed = 27, // MSG-DIST-002 - Assessment distributed to board members notification
        AssessmentCompleted = 28, // Assessment completed notification (all responses collected)
        AssessmentResponseSubmitted = 29, // Individual response submitted notification
        AssessmentReminder = 30 // Assessment response reminder notification
    }
}
