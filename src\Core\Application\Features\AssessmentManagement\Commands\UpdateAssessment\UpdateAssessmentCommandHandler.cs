using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Abstraction.Contracts.Repository;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Domain.Entities.Shared;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contracts.Identity;
using Abstraction.Contract.Service;
using Application.Features.AssessmentManagement.Dtos;
using Abstraction.Constants;
using Domain.Entities.Users;

namespace Application.Features.AssessmentManagement.Commands.UpdateAssessment
{
    /// <summary>
    /// Handler for UpdateAssessmentCommand
    /// Implements comprehensive assessment updating with basic info, questions, and attachments
    /// Follows Clean Architecture, CQRS, RBAC, localization, audit trails, and State Pattern
    /// Based on the same architectural patterns as EditResolutionCommandHandler for consistency
    /// </summary>
    public class UpdateAssessmentCommandHandler : BaseResponseHandler, ICommandHandler<UpdateAssessmentCommand, BaseResponse<string>>
    {

        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IIdentityServiceManager _identityService;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructors
        public UpdateAssessmentCommandHandler(
            IRepositoryManager repository,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            IIdentityServiceManager identityService,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _identityService = identityService;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(UpdateAssessmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting UpdateAssessment operation for ID: {request.Id}");

                // 1. Validate request
                if (request == null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyRequestValidation]);

                if (request.Id <= 0)
                    return BadRequest<string>(_localizer[SharedResourcesKey.InvalidIdValidation]);

                // 2. Get current user information
                var fundDetails = await _repository.Funds.ViewFundUsers(request.FundId, trackChanges: false);
                var currentUserId = _currentUserService.UserId;
                var userRole = await GetUserFundRole(fundDetails, currentUserId.Value);

                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Retrieve existing assessment with related data
                var existingAssessment = await _repository.Assessment.GetAssessmentWithDetailsAsync(request.Id, trackChanges: true);
                if (existingAssessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<string>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // 4. Get fund information for validation
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found for assessment ID: {request.Id}");
                    return NotFound<string>(_localizer[SharedResourcesKey.FundNotFound]);
                }

                // 5. Validate user has permission to edit this assessment
                if (! await HasEditPermission(existingAssessment, userRole, fundDetails.Id, existingAssessment.CreatedBy))
                {
                    _logger.LogWarn($"User {currentUserId} does not have permission to edit assessment {request.Id}");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 6. Validate assessment status using state pattern
                existingAssessment.InitializeState();
                if (!existingAssessment.CanEdit())
                {
                    _logger.LogError(null, $"Cannot edit assessment {request.Id} with status {existingAssessment.Status}");
                    return BadRequest<string>(_localizer[SharedResourcesKey.AssessmentCannotBeEdited]);
                }

                // 7. Store original status for comparison
                var originalStatus = existingAssessment.Status;

                // 8. Update basic assessment properties using AutoMapper
                _mapper.Map(request, existingAssessment);

                // 9. Process assessment questions if provided (for Questionnaire type)
                if (request.Type == AssessmentType.Questionnaire && request.Questions?.Count > 0)
                {
                    await ProcessAssessmentQuestions(existingAssessment, request.Questions);
                }

                // 11. Handle status transitions based on SaveAsDraft flag using state pattern
                await HandleStatusTransition(existingAssessment, request.SaveAsDraft, fundDetails);

                // 12. Save changes
                var updateResult = await _repository.Assessment.UpdateAsync(existingAssessment);
                if (!updateResult)
                {
                    _logger.LogError(null, $"Failed to update assessment with ID: {request.Id}");
                    return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
                }

                // 13. Return success response
                var successMessage = request.SaveAsDraft
                    ? _localizer[SharedResourcesKey.RecordSavedSuccessfully]
                    : _localizer[SharedResourcesKey.AssessmentUpdatedSuccessfully];

                return Success<string>(successMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in UpdateAssessment for ID: {request.Id}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion


        #region Helper Methods

        /// <summary>
        /// Processes assessment questions using soft delete and add new strategy
        /// Soft deletes existing questions and adds new ones from request
        /// Follows established soft delete patterns in the codebase
        /// </summary>
        private async Task ProcessAssessmentQuestions(Assessment assessment, List<CreateAssessmentQuestionDto> requestQuestions)
        {
            // Step 1: Soft delete all existing AssessmentQuestions and their options
            await SoftDeleteExistingAssessmentQuestions(assessment.Id);

            _logger.LogInfo($"Assessment questions processed using soft delete strategy for Assessment ID: {assessment.Id}, New questions count: {requestQuestions.Count}");
        }

        /// <summary>
        /// Soft deletes all existing AssessmentQuestions and their associated options
        /// Follows the established soft delete pattern in the codebase
        /// </summary>
        private async Task SoftDeleteExistingAssessmentQuestions(int assessmentId)
        {
            // Get existing questions for soft delete from repository
            var existingAssessment = await _repository.Assessment.GetAssessmentWithDetailsAsync(assessmentId, trackChanges: true);
            var existingQuestions = existingAssessment?.Questions?.ToList() ?? new List<AssessmentQuestion>();

            if (existingQuestions.Any())
            {
                // Soft delete existing options first
                foreach (var question in existingQuestions)
                {
                    if (question.Options?.Any() == true)
                    {
                        foreach (var option in question.Options)
                        {
                            option.IsDeleted = true;
                            option.DeletedAt = DateTime.Now;
                        }
                    }
                }

                // Soft delete existing questions by setting IsDeleted = true and DeletedAt = DateTime.Now
                foreach (var question in existingQuestions)
                {
                    question.IsDeleted = true;
                    question.DeletedAt = DateTime.Now;
                    // DeletedBy will be set by the audit system
                }

                _logger.LogInfo($"Soft deleted {existingQuestions.Count} existing assessment questions for Assessment ID: {assessmentId}");
            }
        }



        /// <summary>
        /// Handles status transitions based on SaveAsDraft flag using state pattern
        /// Follows the documented state transition matrix for Assessment
        /// </summary>
        private async Task HandleStatusTransition(Assessment assessment, bool saveAsDraft, Fund fundDetails)
        {
            // Initialize state pattern if not already initialized
            if (assessment.StateContext == null)
            {
                assessment.InitializeState();
            }

            // Determine target status based on current status, SaveAsDraft flag, and user role
            var userRole = await GetUserFundRole(fundDetails, _currentUserService.UserId.Value);
            var currentUserName = _currentUserService.UserName ?? "Unknown User";
            var originalStatus = assessment.Status;
            var targetStatus = DetermineTargetStatus(assessment.Status, saveAsDraft, userRole);

            if (assessment.Status != targetStatus)
            {
                // Determine action and localization key based on target status
                AssessmentActionEnum action;
                string localizationKey;
                string actionDescription;

                if (targetStatus == AssessmentStatus.WaitingForApproval)
                {
                    // Use Submission action when transitioning to WaitingForApproval
                    action = AssessmentActionEnum.Submission;
                    localizationKey = SharedResourcesKey.AuditActionAssessmentSubmission;
                    actionDescription = "Assessment submitted for approval";
                }
                else
                {
                    // Use Edit action for other transitions
                    action = AssessmentActionEnum.Edit;
                    localizationKey = SharedResourcesKey.AuditActionAssessmentDataUpdate;
                    actionDescription = "Assessment edited and saved";
                }

                var comprehensiveDetails = $"Assessment edited and status transitioned from {originalStatus} to {targetStatus} by {userRole}: {currentUserName}. Update operation completed including basic information, questions, and attachments as applicable.";
                var transitionSuccess = assessment.ChangeStatusWithAudit(targetStatus,
                    action, actionDescription,
                    localizationKey, _currentUserService.UserId.Value, userRole.ToString(),
                    comprehensiveDetails);

                if (!transitionSuccess)
                {
                    _logger.LogError(null, $"Failed to transition assessment {assessment.Id} from {assessment.Status} to {targetStatus}");
                    throw new InvalidOperationException($"Invalid status transition from {assessment.Status} to {targetStatus}");
                }
            }
            else
            {
                // No status change, but still add comprehensive audit entry for the edit action
                var localizationKey = SharedResourcesKey.AuditActionAssessmentDataUpdate;
                var comprehensiveDetails = $"Assessment data updated without status change by {userRole}: {currentUserName}. Edit operation completed on assessment content while maintaining current {originalStatus} status. Changes may include basic information, questions, or attachments.";
                assessment.StateContext.AddAuditEntry(AssessmentActionEnum.Edit,
                    "Assessment data updated without status change", localizationKey,
                    _currentUserService.UserId.Value, userRole.ToString(),
                    comprehensiveDetails);
            }
        }

        /// <summary>
        /// Determines the target status based on current status, SaveAsDraft flag, and user role
        /// Based on the state transition matrix for Assessment entities
        /// </summary>
        private AssessmentStatus DetermineTargetStatus(AssessmentStatus currentStatus, bool saveAsDraft, Roles userRole)
        {
            // Fund Manager transitions
            if (userRole == Roles.FundManager)
            {
                return currentStatus switch
                {
                    AssessmentStatus.Draft when saveAsDraft => AssessmentStatus.Draft,
                    AssessmentStatus.Draft when !saveAsDraft => AssessmentStatus.WaitingForApproval,
                    _ => currentStatus // No status change for other statuses
                };
            }

            // Legal Council and Board Secretary transitions
            if (userRole == Roles.LegalCouncil || userRole == Roles.BoardSecretary)
            {
                return currentStatus switch
                {
                    AssessmentStatus.Draft when saveAsDraft => AssessmentStatus.Draft,
                    AssessmentStatus.Draft when !saveAsDraft => AssessmentStatus.WaitingForApproval,
                    AssessmentStatus.WaitingForApproval => AssessmentStatus.WaitingForApproval, // Can edit but status stays same
                    AssessmentStatus.Rejected when !saveAsDraft => AssessmentStatus.WaitingForApproval, // Edit and resubmit
                    _ => currentStatus // No status change for other statuses
                };
            }

            // Default: no status change
            return currentStatus;
        }

        /// <summary>
        /// Sets action permissions based on user role and assessment status
        /// Based on Assessment business rules for user permissions
        /// </summary>
        private async Task<bool> HasEditPermission(Assessment assessment, Roles userRole, int fundId, int creatorID)
        {
            var isFundManager = userRole == Roles.FundManager;
            var isLegalCouncilOrBoardSecretary = userRole == Roles.LegalCouncil || userRole == Roles.BoardSecretary;
            bool canEdit = false;

            // Set permissions based on status and role
            switch (assessment.Status)
            {
                case AssessmentStatus.Draft:
                    // Only Fund Manager can edit draft assessments they created
                    canEdit = isFundManager && _currentUserService.UserId == creatorID;
                    break;

                case AssessmentStatus.WaitingForApproval:
                    // Legal Council and Board Secretary can edit assessments waiting for approval
                    canEdit = isLegalCouncilOrBoardSecretary;
                    break;

                case AssessmentStatus.Rejected:
                    // Legal Council and Board Secretary can edit rejected assessments
                    canEdit = isLegalCouncilOrBoardSecretary;
                    break;

                case AssessmentStatus.Approved:
                case AssessmentStatus.Active:
                case AssessmentStatus.Completed:
                    // Read-only for all these statuses
                    canEdit = false;
                    break;

                default:
                    // Unknown status, no actions allowed
                    break;
            }
            return canEdit;
        }

        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// </summary>
        /// <param name="fundDetails">The fund details to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User role in the fund context</returns>
        private async Task<Roles> GetUserFundRole(Fund fundDetails, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");

                var userRole = Roles.None;

                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundDetails.Id}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundDetails.Id}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundDetails.Id}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundDetails.Id}");
                    }
                }

                // 4. Check if user is a Board Member for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundDetails.Id}");
                    }
                }

                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundDetails.Id}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                return Roles.None;
            }
        }

        #endregion
    }
}
