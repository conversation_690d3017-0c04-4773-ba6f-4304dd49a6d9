using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Basic Information in Results
    /// Arabic: كائل نقل البيانات للمعلومات الأساسية للتقييم في النتائج
    /// </summary>
    public record AssessmentBasicInfoDto
    {
        /// <summary>
        /// Assessment ID
        /// Arabic: معرف التقييم
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment type
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Fund name
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;

        /// <summary>
        /// Distribution date
        /// Arabic: تاريخ التوزيع
        /// </summary>
        public DateTime? DistributionDate { get; set; }

        /// <summary>
        /// Due date
        /// Arabic: تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Completion date
        /// Arabic: تاريخ الإكمال
        /// </summary>
        public DateTime? CompletionDate { get; set; }
    }
}
