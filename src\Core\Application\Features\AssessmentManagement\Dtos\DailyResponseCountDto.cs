namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Daily Response Count
    /// Arabic: كائل نقل البيانات لعدد الردود اليومية
    /// </summary>
    public record DailyResponseCountDto
    {
        /// <summary>
        /// Date
        /// Arabic: التاريخ
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Number of responses on this date
        /// Arabic: عدد الردود في هذا التاريخ
        /// </summary>
        public int ResponseCount { get; set; }

        /// <summary>
        /// Cumulative response count up to this date
        /// Arabic: العدد التراكمي للردود حتى هذا التاريخ
        /// </summary>
        public int CumulativeCount { get; set; }
    }
}
