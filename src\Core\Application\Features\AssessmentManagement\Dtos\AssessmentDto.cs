using Abstraction.Base.Dto;
using Application.Common.Dtos;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// Base Data Transfer Object for Assessment entity
    /// Contains common properties shared across different Assessment operations
    /// Based on AssessmentStories.md requirements and Clean Architecture principles
    /// Follows the same pattern as ResolutionDto for consistency
    /// </summary>
    public record AssessmentDto : BaseDto
    {
        /// <summary>
        /// Assessment title (required)
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description (optional)
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment type (Questionnaire or Attachment)
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Fund ID this assessment belongs to
        /// Arabic: معرف الصندوق
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Fund name for display purposes
        /// Arabic: اسم الصندوق
        /// </summary>
        public string? FundName { get; set; }

        /// <summary>
        /// Due date for assessment responses (optional)
        /// Arabic: تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Instructions for board members (optional)
        /// Arabic: تعليمات لأعضاء مجلس الإدارة
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Whether the assessment allows anonymous responses
        /// Arabic: السماح بالردود المجهولة
        /// </summary>
        public bool AllowAnonymousResponses { get; set; } = false;

        /// <summary>
        /// Whether responses can be edited after submission
        /// Arabic: السماح بتعديل الردود
        /// </summary>
        public bool AllowResponseEditing { get; set; } = true;

        /// <summary>
        /// Assessment questions (for Questionnaire type)
        /// Arabic: أسئلة التقييم
        /// </summary>
        //public List<AssessmentQuestionDto>? Questions { get; set; }

        /// <summary>
        /// Assessment attachments (for Attachment type or supporting documents)
        /// Arabic: مرفقات التقييم
        /// </summary>
        //public List<AssessmentAttachmentDto>? Attachments { get; set; }

        /// <summary>
        /// Status display name for UI
        /// Arabic: اسم الحالة للعرض
        /// </summary>
        public string? StatusDisplayName { get; set; }

        /// <summary>
        /// Created by user name for display
        /// Arabic: اسم المنشئ
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// Creation date for display
        /// Arabic: تاريخ الإنشاء
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Last updated date for display
        /// Arabic: تاريخ آخر تحديث
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// DTO representing an assessment status for API responses
    /// Inherits from LocalizedDto to provide automatic Arabic/English localization
    /// Used for populating dropdowns and filters in the frontend
    /// Follows the same pattern as ResolutionStatusDto for consistency
    /// </summary>
    public record AssessmentStatusDto : LocalizedDto
    {
        /// <summary>
        /// Status enum value
        /// </summary>
        public AssessmentStatus Value { get; set; }

        /// <summary>
        /// Description of the status from the enum Description attribute
        /// </summary>
        public string Description { get; set; } = string.Empty;

        // Note: Id, NameAr, NameEn, and LocalizedName are inherited from LocalizedDto
        // LocalizedName automatically returns the appropriate name based on current culture
    }

    /// <summary>
    /// DTO representing an assessment type for API responses
    /// Inherits from LocalizedDto to provide automatic Arabic/English localization
    /// Used for populating dropdowns and filters in the frontend
    /// Follows the same pattern as ResolutionTypeDto for consistency
    /// </summary>
    public record AssessmentTypeDto : LocalizedDto
    {
        /// <summary>
        /// Type enum value
        /// </summary>
        public AssessmentType Value { get; set; }

        /// <summary>
        /// Description of the type from the enum Description attribute
        /// </summary>
        public string Description { get; set; } = string.Empty;

        // Note: Id, NameAr, NameEn, and LocalizedName are inherited from LocalizedDto
        // LocalizedName automatically returns the appropriate name based on current culture
    }
}
