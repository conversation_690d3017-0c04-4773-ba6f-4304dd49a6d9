namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Option Analysis
    /// Arabic: كائل نقل البيانات لتحليل الخيارات
    /// </summary>
    public record OptionAnalysisDto
    {
        /// <summary>
        /// Option ID
        /// Arabic: معرف الخيار
        /// </summary>
        public int OptionId { get; set; }

        /// <summary>
        /// Option text
        /// Arabic: نص الخيار
        /// </summary>
        public string OptionText { get; set; } = string.Empty;

        /// <summary>
        /// Number of times this option was selected
        /// Arabic: عدد مرات اختيار هذا الخيار
        /// </summary>
        public int SelectionCount { get; set; }

        /// <summary>
        /// Percentage of responses that selected this option
        /// Arabic: نسبة الردود التي اختارت هذا الخيار
        /// </summary>
        public decimal SelectionPercentage { get; set; }

        /// <summary>
        /// Whether this option is correct
        /// Arabic: الخيار صحيح
        /// </summary>
        public bool IsCorrect { get; set; }
    }
}
