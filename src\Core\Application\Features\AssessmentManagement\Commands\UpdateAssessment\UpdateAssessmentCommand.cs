using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Commands.UpdateAssessment
{
    /// <summary>
    /// Command for updating an existing assessment (draft only)
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as EditResolutionCommand for consistency
    /// </summary>
    public record UpdateAssessmentCommand : UpdateAssessmentDto, ICommand<BaseResponse<string>>
    {
        // Command inherits all properties from UpdateAssessmentDto
        // No additional properties needed unless specific to command execution
    }
}
