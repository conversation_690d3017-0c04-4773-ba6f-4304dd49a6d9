using Domain.Entities.AssessmentManagement.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.AssessmentManagement.State
{
    /// <summary>
    /// Interface for Assessment State Pattern implementation
    /// Defines the contract for all assessment states and their behaviors
    /// Based on AssessmentStories.md requirements and Clean Architecture principles
    /// Follows the same pattern as IResolutionState for consistency
    /// </summary>
    public interface IAssessmentState
    {
        /// <summary>
        /// Gets the status enum value for this state
        /// Maps to AssessmentStatus enum values
        /// </summary>
        AssessmentStatus Status { get; }

        /// <summary>
        /// Handles the state-specific logic and transitions
        /// </summary>
        /// <param name="assessment">The assessment entity to operate on</param>
        void Handle(Assessment assessment);

        /// <summary>
        /// Determines if this state can transition to the target status
        /// </summary>
        /// <param name="targetStatus">Target status to transition to</param>
        /// <returns>True if transition is allowed, false otherwise</returns>
        bool CanTransitionTo(AssessmentStatus targetStatus);

        /// <summary>
        /// Gets the allowed transition statuses from this state
        /// </summary>
        /// <returns>Collection of allowed target statuses</returns>
        IEnumerable<AssessmentStatus> GetAllowedTransitions();

        /// <summary>
        /// Validates if the current state allows editing
        /// </summary>
        /// <returns>True if editing is allowed in this state</returns>
        bool CanEdit();

        /// <summary>
        /// Validates if the current state allows deletion
        /// </summary>
        /// <returns>True if deletion is allowed in this state</returns>
        bool CanDelete();

        /// <summary>
        /// Validates if the current state allows submission for approval
        /// </summary>
        /// <returns>True if submission is allowed in this state</returns>
        bool CanSubmitForApproval();

        /// <summary>
        /// Validates if the current state allows approval
        /// </summary>
        /// <returns>True if approval is allowed in this state</returns>
        bool CanApprove();

        /// <summary>
        /// Validates if the current state allows rejection
        /// </summary>
        /// <returns>True if rejection is allowed in this state</returns>
        bool CanReject();

        /// <summary>
        /// Validates if the current state allows distribution to board members
        /// </summary>
        /// <returns>True if distribution is allowed in this state</returns>
        bool CanDistribute();

        /// <summary>
        /// Validates if the current state allows board member responses
        /// </summary>
        /// <returns>True if responses are allowed in this state</returns>
        bool CanReceiveResponses();

        /// <summary>
        /// Validates if the current state allows viewing results
        /// </summary>
        /// <returns>True if viewing results is allowed in this state</returns>
        bool CanViewResults();

        /// <summary>
        /// Gets the state-specific business rules and validation messages
        /// </summary>
        /// <returns>Collection of validation messages for this state</returns>
        IEnumerable<string> GetValidationMessages();

        /// <summary>
        /// Gets the localized resource key for the current state description
        /// </summary>
        /// <returns>Resource key for state description</returns>
        string GetStateDescriptionKey();

        /// <summary>
        /// Gets the localized resource key for the current state display name
        /// Used for UI display purposes
        /// </summary>
        /// <returns>Resource key for state display name</returns>
        string GetStateDisplayNameKey();

        /// <summary>
        /// Validates if the assessment can be completed (all responses collected)
        /// </summary>
        /// <param name="assessment">The assessment to validate</param>
        /// <returns>True if assessment can be marked as completed</returns>
        bool CanMarkAsCompleted(Assessment assessment);
    }
}
