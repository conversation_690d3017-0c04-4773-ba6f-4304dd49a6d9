using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for AssessmentStatusHistory entity
    /// Configures navigation properties, relationships, and constraints for assessment status tracking
    /// Provides comprehensive configuration for audit trail and status change history
    /// </summary>
    public class AssessmentStatusHistoryEntityConfig : IEntityTypeConfiguration<AssessmentStatusHistory>
    {
        public void Configure(EntityTypeBuilder<AssessmentStatusHistory> builder)
        {
            // Configure primary key
            builder.HasKey(ash => ash.Id);

            // Configure required properties with constraints
            builder.Property(ash => ash.AssessmentId)
                .IsRequired()
                .HasComment("Foreign key reference to the Assessment entity");

            builder.Property(ash => ash.AssessmentStatusId)
                .IsRequired()
                .HasComment("Assessment status ID at the time of this history entry");

            builder.Property(ash => ash.Action)
                .IsRequired()
                .HasComment("Action that was performed (enum value)");

            // Configure optional properties
            builder.Property(ash => ash.Reason)
                .HasMaxLength(2000)
                .HasComment("Reason for the action/transition");

            builder.Property(ash => ash.RejectionReason)
                .HasMaxLength(2000)
                .HasComment("Rejection reason (specific for rejection actions)");

            builder.Property(ash => ash.ActionDetails)
                .HasMaxLength(4000)
                .HasComment("Comprehensive description of the operation performed");

            builder.Property(ash => ash.Notes)
                .HasMaxLength(1000)
                .HasComment("Localization key reference (NOT translated text) for retrieval-time localization");

            builder.Property(ash => ash.UserRole)
                .HasMaxLength(100)
                .HasComment("User role performing the action");

            builder.Property(ash => ash.PreviousStatus)
                .HasComment("Previous status (for status changes)");

            builder.Property(ash => ash.NewStatus)
                .HasComment("New status (for status changes)");

            // Configure relationships
            builder.HasOne(ash => ash.Assessment)
                .WithMany(a => a.StatusHistories)
                .HasForeignKey(ash => ash.AssessmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentStatusHistories_Assessments_AssessmentId");

            builder.HasOne(ash => ash.CreatedByUser)
                .WithMany()
                .HasForeignKey(ash => ash.CreatedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentStatusHistories_Users_CreatedBy");

            // Configure indexes for performance optimization
            builder.HasIndex(ash => ash.AssessmentId)
                .HasDatabaseName("IX_AssessmentStatusHistories_AssessmentId_Performance");

            builder.HasIndex(ash => ash.Action)
                .HasDatabaseName("IX_AssessmentStatusHistories_Action_Performance");

            builder.HasIndex(ash => ash.CreatedAt)
                .HasDatabaseName("IX_AssessmentStatusHistories_CreatedAt_Performance");

            builder.HasIndex(ash => new { ash.AssessmentId, ash.CreatedAt })
                .HasDatabaseName("IX_AssessmentStatusHistories_AssessmentId_CreatedAt_Performance");

            builder.HasIndex(ash => ash.PreviousStatus)
                .HasDatabaseName("IX_AssessmentStatusHistories_PreviousStatus_Performance")
                .HasFilter("[PreviousStatus] IS NOT NULL");

            builder.HasIndex(ash => ash.NewStatus)
                .HasDatabaseName("IX_AssessmentStatusHistories_NewStatus_Performance")
                .HasFilter("[NewStatus] IS NOT NULL");

            // Configure table name
            builder.ToTable("AssessmentStatusHistories");

           
        }
    }
}
