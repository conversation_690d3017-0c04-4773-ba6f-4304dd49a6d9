using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment details by ID endpoint
    /// Contains specific fields required for the GET /api/assessments/{id} endpoint
    /// Based on API specification requirements
    /// Arabic: كائل نقل البيانات لتفاصيل التقييم حسب المعرف
    /// </summary>
    public record AssessmentByIdDto : BaseDto
    {
        /// <summary>
        /// Fund information
        /// Arabic: معلومات الصندوق
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Fund name
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;

        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment due date
        /// Arabic: تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Assessment instructions
        /// Arabic: تعليمات التقييم
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Assessment type (Questionnaire or Attachment)
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Questions list (for Questionnaire type assessments)
        /// Arabic: قائمة الأسئلة
        /// </summary>
        public List<AssessmentQuestionByIdDto>? Questions { get; set; }

        /// <summary>
        /// Attachment details (for Attachment type assessments)
        /// Arabic: تفاصيل المرفق
        /// </summary>
        public AssessmentAttachmentByIdDto? Attachment { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Question in the by ID response
    /// Arabic: كائل نقل البيانات لسؤال التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentQuestionByIdDto : BaseDto
    {
        /// <summary>
        /// Question text
        /// Arabic: نص السؤال
        /// </summary>
        public string QuestionText { get; set; } = string.Empty;

        /// <summary>
        /// Question type
        /// Arabic: نوع السؤال
        /// </summary>
        public QuestionType QuestionType { get; set; }

        /// <summary>
        /// Question display order
        /// Arabic: ترتيب عرض السؤال
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Whether the question is required
        /// Arabic: السؤال مطلوب
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Question options (for choice-type questions)
        /// Arabic: خيارات السؤال
        /// </summary>
        public List<AssessmentOptionByIdDto>? Options { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Option in the by ID response
    /// Arabic: كائل نقل البيانات لخيار التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentOptionByIdDto : BaseDto
    {
        /// <summary>
        /// Option text/value
        /// Arabic: نص الخيار
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Option display order
        /// Arabic: ترتيب عرض الخيار
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Whether this option is correct
        /// Arabic: الخيار صحيح
        /// </summary>
        public bool IsCorrect { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Attachment in the by ID response
    /// Arabic: كائل نقل البيانات لمرفق التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentAttachmentByIdDto : BaseDto
    {
        /// <summary>
        /// Original file name
        /// Arabic: اسم الملف الأصلي
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// Arabic: حجم الملف بالبايت
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// File content type (MIME type)
        /// Arabic: نوع محتوى الملف
        /// </summary>
        public string ContentType { get; set; } = string.Empty;
    }
}
